/**
 * @description 区分本地打开窗口的示例
 * @description 展示如何区分活跃窗口中哪些是在本地打开的
 */

const { getAllBrowserList, getLocalOpenBrowsersDetail } = require('./request')

/**
 * @description 获取并分类所有窗口
 */
async function categorizeAllBrowsers() {
  console.log('=== 获取并分类所有浏览器窗口 ===\n')
  
  try {
    // 1. 获取所有窗口列表
    console.log('📋 正在获取所有窗口列表...')
    const allBrowsers = await getAllBrowserList()
    
    // 2. 获取本地打开的窗口详情
    console.log('🔍 正在检查本地打开的窗口...')
    const localOpenResult = await getLocalOpenBrowsersDetail()
    
    if (!localOpenResult.success) {
      console.error('❌ 获取本地打开窗口失败:', localOpenResult.error)
      return
    }
    
    // 3. 分类统计
    const localOpenIds = localOpenResult.localOpenBrowsers.map(b => b.id)
    const categories = {
      localOpen: localOpenResult.localOpenBrowsers,
      activeOnly: allBrowsers.filter(b => b.status === 1 && !localOpenIds.includes(b.id)),
      inactive: allBrowsers.filter(b => b.status !== 1)
    }
    
    // 4. 显示结果
    console.log('\n🔥 本地打开的窗口:')
    if (categories.localOpen.length > 0) {
      categories.localOpen.forEach((browser, index) => {
        console.log(`  ${index + 1}. [PID: ${browser.pid}] ${browser.name} (ID: ${browser.id})`)
      })
    } else {
      console.log('  无本地打开的窗口')
    }
    
    console.log('\n⚠️  仅活跃的窗口 (未在本地打开):')
    if (categories.activeOnly.length > 0) {
      categories.activeOnly.slice(0, 5).forEach((browser, index) => {
        console.log(`  ${index + 1}. ${browser.name} (ID: ${browser.id})`)
      })
      if (categories.activeOnly.length > 5) {
        console.log(`  ... 还有 ${categories.activeOnly.length - 5} 个窗口`)
      }
    } else {
      console.log('  无仅活跃的窗口')
    }
    
    // 5. 统计摘要
    console.log('\n📊 统计摘要:')
    console.log(`  总窗口数: ${allBrowsers.length}`)
    console.log(`  本地打开: ${categories.localOpen.length}`)
    console.log(`  仅活跃: ${categories.activeOnly.length}`)
    console.log(`  非活跃: ${categories.inactive.length}`)
    
    return categories
    
  } catch (error) {
    console.error('❌ 分类窗口时出错:', error.message)
  }
}

/**
 * @description 仅获取本地打开的窗口
 */
async function getOnlyLocalOpenBrowsers() {
  console.log('\n=== 仅获取本地打开的窗口 ===\n')
  
  try {
    const localOpenResult = await getLocalOpenBrowsersDetail()
    
    if (!localOpenResult.success) {
      console.error('❌ 获取本地打开窗口失败:', localOpenResult.error)
      return []
    }
    
    console.log(`🔥 找到 ${localOpenResult.count} 个本地打开的窗口:`)
    
    localOpenResult.localOpenBrowsers.forEach((browser, index) => {
      console.log(`\n${index + 1}. 窗口信息:`)
      console.log(`   名称: ${browser.name}`)
      console.log(`   ID: ${browser.id}`)
      console.log(`   状态: ${browser.status}`)
      console.log(`   PID: ${browser.pid}`)
      console.log(`   用户名: ${browser.userName}`)
      console.log(`   平台: ${browser.platform}`)
      console.log(`   分组: ${browser.groupName}`)
      console.log(`   最后IP: ${browser.lastIp}`)
      console.log(`   最后国家: ${browser.lastCountry}`)
    })
    
    return localOpenResult.localOpenBrowsers
    
  } catch (error) {
    console.error('❌ 获取本地打开窗口时出错:', error.message)
    return []
  }
}

/**
 * @description 检查指定窗口是否在本地打开
 */
async function checkIfBrowserIsLocalOpen(browserId) {
  console.log(`\n=== 检查窗口 ${browserId} 是否在本地打开 ===\n`)
  
  try {
    const localOpenResult = await getLocalOpenBrowsersDetail()
    
    if (!localOpenResult.success) {
      console.error('❌ 获取本地打开窗口失败:', localOpenResult.error)
      return false
    }
    
    const isLocalOpen = localOpenResult.localOpenBrowsers.some(b => b.id === browserId)
    
    if (isLocalOpen) {
      const browser = localOpenResult.localOpenBrowsers.find(b => b.id === browserId)
      console.log(`✅ 窗口 "${browser.name}" 正在本地打开`)
      console.log(`   PID: ${browser.pid}`)
      console.log(`   状态: ${browser.status}`)
    } else {
      console.log(`❌ 窗口 ${browserId} 未在本地打开`)
    }
    
    return isLocalOpen
    
  } catch (error) {
    console.error('❌ 检查窗口状态时出错:', error.message)
    return false
  }
}

// 主函数
async function main() {
  // 示例1: 获取并分类所有窗口
  await categorizeAllBrowsers()
  
  // 示例2: 仅获取本地打开的窗口
  await getOnlyLocalOpenBrowsers()
  
  // 示例3: 检查特定窗口是否在本地打开
  // await checkIfBrowserIsLocalOpen('your-browser-id-here')
}

// 运行示例
if (require.main === module) {
  main().catch(console.error)
}

module.exports = {
  categorizeAllBrowsers,
  getOnlyLocalOpenBrowsers,
  checkIfBrowserIsLocalOpen
}
