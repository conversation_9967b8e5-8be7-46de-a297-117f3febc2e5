{"name": "selenium-webdriver", "version": "4.1.2", "description": "The official WebDriver JavaScript bindings from the Selenium project", "license": "Apache-2.0", "keywords": ["automation", "selenium", "testing", "webdriver", "webdriverjs"], "homepage": "https://github.com/SeleniumHQ/selenium/tree/trunk/javascript/node/selenium-webdriver#readme", "bugs": {"url": "https://github.com/SeleniumHQ/selenium/issues"}, "main": "./index", "repository": {"type": "git", "url": "https://github.com/SeleniumHQ/selenium.git"}, "engines": {"node": ">= 10.15.0"}, "dependencies": {"jszip": "^3.6.0", "tmp": "^0.2.1", "ws": ">=7.4.6"}, "devDependencies": {"eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-no-only-tests": "^2.6.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "express": "^4.17.1", "jasmine": "^3.8.0", "mocha": "^9.2.2", "multer": "^1.4.2", "prettier": "^2.3.0", "serve-index": "^1.9.1", "sinon": "^10.0.0"}, "scripts": {"lint": "eslint --ignore-pattern node_modules --ignore-pattern generator --ext js lib/http.js \"**/*.js\"", "test": "npm run lint && mocha -t 600000 --recursive test", "test-jasmine": "jasmine JASMINE_CONFIG_PATH=jasmine.json"}, "mocha": {"recursive": true, "timeout": 600000}}