name: pull-request

on:
  pull_request:
    branches: [ master ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - uses: actions/setup-node@v2
        with:
          node-version: 'lts/*'
          cache: 'npm'

      - name: Cache Node modules
        uses: actions/cache@v2
        env:
          cache-name: cache-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: "Install dependencies"
        run: |
          npm install
          sudo npx playwright install-deps

      - name: Lint
        run: npm run lint
      - name: Test Node
        run: npm run test-node
      - name: Test browsers
        run: |
          npm run test-browser
