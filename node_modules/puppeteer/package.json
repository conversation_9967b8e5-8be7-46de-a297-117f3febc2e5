{"name": "puppeteer", "version": "13.7.0", "description": "A high-level API to control headless Chrome over the DevTools Protocol", "keywords": ["puppeteer", "chrome", "headless", "automation"], "main": "./cjs-entry.js", "types": "lib/types.d.ts", "repository": "github:puppeteer/puppeteer", "engines": {"node": ">=10.18.1"}, "scripts": {"test-browser": "wtr", "test-browser-watch": "wtr --watch", "unit": "npm run tsc-cjs && mocha --config mocha-config/puppeteer-unit-tests.js", "chrome-headless-unit": "cross-env HEADLESS=chrome npm run unit", "unit-debug": "npm run tsc-cjs && mocha --inspect-brk --config mocha-config/puppeteer-unit-tests.js", "unit-with-coverage": "cross-env COVERAGE=1 npm run unit", "assert-unit-coverage": "cross-env COVERAGE=1 mocha --config mocha-config/coverage-tests.js", "funit": "cross-env PUPPETEER_PRODUCT=firefox npm run unit", "test": "npm run tsc && npm run lint --silent && npm run unit-with-coverage && npm run test-browser", "prepare": "node typescript-if-required.js && husky install", "prepublishOnly": "npm run build", "dev-install": "npm run tsc && node install.js", "install": "node install.js", "eslint": "([ \"$CI\" = true ] && eslint --ext js --ext ts --quiet -f codeframe . || eslint --ext js --ext ts .)", "eslint-fix": "eslint --ext js --ext ts --fix .", "commitlint": "commitlint --from=HEAD~1", "prettier": "prettier --check .", "prettier-fix": "prettier --write .", "lint": "npm run eslint && npm run build && npm run doc && npm run prettier", "doc": "node utils/doclint/cli.js", "generate-api-docs-for-testing": "commonmark docs/api.md > docs/api.html", "clean-lib": "<PERSON><PERSON><PERSON> lib", "build": "npm run tsc && npm run generate-d-ts", "tsc": "npm run clean-lib && tsc --version && npm run tsc-cjs && npm run tsc-esm", "tsc-cjs": "tsc -b src/tsconfig.cjs.json", "tsc-esm": "tsc -b src/tsconfig.esm.json", "apply-next-version": "node utils/apply_next_version.js", "test-install": "scripts/test-install.sh", "clean-docs": "rimraf website/docs && rimraf docs-api-json", "generate-d-ts": "npm run clean-docs && api-extractor run --local --verbose", "generate-docs": "npm run generate-d-ts && api-documenter markdown -i docs-api-json -o website/docs && node utils/remove-tag.js", "ensure-correct-devtools-protocol-revision": "ts-node -s scripts/ensure-correct-devtools-protocol-package", "ensure-pinned-deps": "ts-node -s scripts/ensure-pinned-deps", "test-types-file": "ts-node -s scripts/test-ts-definition-files.ts", "release": "node utils/remove_version_suffix.js && standard-version --commit-all", "build-docs-production": "cd website && npm install && npm run build"}, "files": ["lib/types.d.ts", "lib/**/*.d.ts", "lib/**/*.d.ts.map", "lib/**/*.js", "lib/**/*.js.map", "install.js", "typescript-if-required.js", "cjs-entry.js", "cjs-entry-core.js"], "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"cross-fetch": "3.1.5", "debug": "4.3.4", "devtools-protocol": "0.0.981744", "extract-zip": "2.0.1", "https-proxy-agent": "5.0.1", "pkg-dir": "4.2.0", "progress": "2.0.3", "proxy-from-env": "1.1.0", "rimraf": "3.0.2", "tar-fs": "2.1.1", "unbzip2-stream": "1.4.3", "ws": "8.5.0"}, "devDependencies": {"@commitlint/cli": "16.2.3", "@commitlint/config-conventional": "16.2.1", "@microsoft/api-documenter": "7.17.11", "@microsoft/api-extractor": "7.22.2", "@types/debug": "4.1.7", "@types/mime": "2.0.3", "@types/mocha": "9.1.1", "@types/node": "17.0.26", "@types/progress": "2.0.5", "@types/proxy-from-env": "1.0.1", "@types/rimraf": "3.0.2", "@types/sinon": "10.0.11", "@types/tar-fs": "2.0.1", "@types/ws": "8.5.3", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "@web/test-runner": "0.13.27", "commonmark": "0.30.0", "cross-env": "7.0.3", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-mocha": "10.0.4", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-unicorn": "42.0.0", "esprima": "4.0.1", "expect": "25.2.7", "husky": "7.0.4", "jpeg-js": "0.4.3", "mime": "3.0.0", "minimist": "1.2.6", "mocha": "9.2.2", "ncp": "2.0.0", "pixelmatch": "5.3.0", "pngjs": "6.0.0", "prettier": "2.6.2", "sinon": "13.0.2", "source-map-support": "0.5.21", "standard-version": "9.3.2", "text-diff": "1.0.1", "ts-node": "10.7.0", "typescript": "4.6.3"}}