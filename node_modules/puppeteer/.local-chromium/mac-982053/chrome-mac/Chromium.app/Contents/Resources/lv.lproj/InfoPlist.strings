CFBundleGetInfoString = "Chromium 101.0.4950.0, Autortiesības 2022 Chromium autori. Visas tiesības paturētas.";
NSBluetoothAlwaysUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piek<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSBluetoothPeripheralUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSCameraUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSHumanReadableCopyright = "Autortiesības 2022 Chromium autori. Visas tiesības paturētas.";
NSLocationUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
NSMicrophoneUsageDescription = "Tiklīdz pārlūkam Chromium tiks sniegta piekļ<PERSON>, vietnes varēs lūgt jums piekļuvi.";
