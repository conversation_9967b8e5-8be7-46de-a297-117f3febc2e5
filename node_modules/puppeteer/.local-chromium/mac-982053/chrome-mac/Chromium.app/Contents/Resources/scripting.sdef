<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE dictionary SYSTEM "file://localhost/System/Library/DTDs/sdef.dtd">
<dictionary title="Dictionary">
  <!--
    STANDARD SUITE
  -->
  <suite name="Standard Suite" code="core" description="Common classes and commands for all applications.">

    <class name="application" code="capp" description="The application&apos;s top-level scripting object.">
      <cocoa class="BrowserCrApplication"/>
      <element description="The windows contained within this application, ordered front to back." type="window">
        <cocoa key="appleScriptWindows"/>
      </element>
      <property name="name" code="pnam" description="The name of the application." type="text" access="r"/>
      <property name="frontmost" code="pisf" description="Is this the frontmost (active) application?" type="boolean" access="r">
        <cocoa key="isActive"/>
      </property>
      <property name="version" code="vers" description="The version of the application." type="text" access="r"/>
      <responds-to command="open">
        <cocoa method="handleOpenScriptCommand:"/>
      </responds-to>
      <responds-to command="print">
        <cocoa method="handlePrintScriptCommand:"/>
      </responds-to>
      <responds-to command="quit">
        <cocoa method="handleQuitScriptCommand:"/>
      </responds-to>
    </class>

    <class name="window" code="cwin" description="A window.">
      <cocoa class="WindowAppleScript"/>
      <element description="The tabs contained within the window." type="tab">
        <cocoa key="tabs"/>
      </element>
      <!-- "given name" is the user title as set by Window: Name Window... -->
      <property name="given name" code="GNam" description="The given name of the window." type="text">
        <cocoa key="givenName"/>
      </property>
      <!-- "name" is an alias for "title" -->
      <property name="name" code="pnam" description="The full title of the window." type="text" access="r">
        <cocoa key="title"/>
      </property>
      <property name="id" code="ID  " description="The unique identifier of the window." type="integer" access="r">
        <cocoa key="uniqueID"/>
      </property>
      <property name="index" code="pidx" description="The index of the window, ordered front to back." type="integer">
        <cocoa key="orderedIndex"/>
      </property>
      <property name="bounds" code="pbnd" description="The bounding rectangle of the window." type="rectangle">
        <cocoa key="boundsAsQDRect"/>
      </property>
      <property name="closeable" code="hclb" description="Whether the window has a close box." type="boolean" access="r">
        <cocoa key="hasCloseBox"/>
      </property>
      <property name="minimizable" code="ismn" description="Whether the window can be minimized." type="boolean" access="r">
        <cocoa key="isMiniaturizable"/>
      </property>
      <property name="minimized" code="pmnd" description="Whether the window is currently minimized." type="boolean">
        <cocoa key="isMiniaturized"/>
      </property>
      <property name="resizable" code="prsz" description="Whether the window can be resized." type="boolean" access="r">
        <cocoa key="isResizable"/>
      </property>
      <property name="visible" code="pvis" description="Whether the window is currently visible." type="boolean">
        <cocoa key="isVisible"/>
      </property>
      <property name="zoomable" code="iszm" description="Whether the window can be zoomed." type="boolean" access="r">
        <cocoa key="isZoomable"/>
      </property>
      <property name="zoomed" code="pzum" description="Whether the window is currently zoomed." type="boolean">
        <cocoa key="isZoomed"/>
      </property>
      <property name="active tab" code="acTa" description="Returns the currently selected tab" type="tab" access="r">
        <cocoa key="activeTab"/>
      </property>
      <property name="mode" code="mode" description="Represents the mode of the window which can be &apos;normal&apos; or &apos;incognito&apos;, can be set only once during creation of the window." type="text">
        <cocoa key="mode"/>
      </property>
      <property name="active tab index" code="acTI" description="The index of the active tab." type="integer"/>
      <responds-to command="close">
        <cocoa method="handlesCloseScriptCommand:"/>
      </responds-to>
    </class>

    <command name="save" code="coresave" description="Save an object.">
      <access-group identifier="*"/>
      <direct-parameter description="the object to save, usually a document or window" type="specifier"/>
      <parameter name="in" code="kfil" description="The file in which to save the object." type="file" optional="yes">
        <cocoa key="File"/>
      </parameter>
      <parameter name="as" code="fltp" description="The file type in which to save the data. Can be &apos;only html&apos; or &apos;complete html&apos;, default is &apos;complete html&apos;." type="text" optional="yes">
        <cocoa key="FileType"/>
      </parameter>
    </command>

    <!--
        According to TN2106, 'open' should return the resulting document
        object.  However, the Cocoa implementation does not do this yet.
      <result type="specifier"/>
      -->
    <command name="open" code="aevtodoc" description="Open a document.">
      <direct-parameter description="The file(s) to be opened.">
        <type type="file" list="yes"/>
      </direct-parameter>
    </command>

    <command name="close" code="coreclos" description="Close a window.">
      <access-group identifier="*"/>
      <cocoa class="NSCloseCommand"/>
      <direct-parameter description="the document(s) or window(s) to close." type="specifier"/>
    </command>

    <command name="quit" code="aevtquit" description="Quit the application.">
      <cocoa class="NSQuitCommand"/>
    </command>

    <command name="count" code="corecnte" description="Return the number of elements of a particular class within an object.">
      <cocoa class="NSCountCommand"/>
      <access-group identifier="*"/>
      <direct-parameter description="the object whose elements are to be counted" type="specifier"/>
      <parameter name="each" code="kocl" description="The class of objects to be counted." type="type" optional="yes">
        <cocoa key="ObjectClass"/>
      </parameter>
      <result description="the number of elements" type="integer"/>
    </command>

    <command name="delete" code="coredelo" description="Delete an object.">
      <cocoa class="NSDeleteCommand"/>
      <access-group identifier="*"/>
      <direct-parameter description="the object to delete" type="specifier"/>
    </command>

    <command name="duplicate" code="coreclon" description="Copy object(s) and put the copies at a new location.">
      <cocoa class="NSCloneCommand"/>
      <access-group identifier="*"/>
      <direct-parameter description="the object(s) to duplicate" type="specifier"/>
      <parameter name="to" code="insh" description="The location for the new object(s)." type="location specifier" optional="yes">
        <cocoa key="ToLocation"/>
      </parameter>
      <parameter name="with properties" code="prdt" description="Properties to be set in the new duplicated object(s)." type="record" optional="yes">
        <cocoa key="WithProperties"/>
      </parameter>
      <result description="the duplicated object(s)" type="specifier"/>
    </command>

    <command name="exists" code="coredoex" description="Verify if an object exists.">
      <cocoa class="NSExistsCommand"/>
      <access-group identifier="*"/>
      <direct-parameter description="the object in question" type="any"/>
      <result description="true if it exists, false if not" type="boolean"/>
    </command>

    <command name="make" code="corecrel" description="Make a new object.">
      <cocoa class="NSCreateCommand"/>
      <access-group identifier="*"/>
      <parameter name="new" code="kocl" description="The class of the new object." type="type">
        <cocoa key="ObjectClass"/>
      </parameter>
      <parameter name="at" code="insh" description="The location at which to insert the object." type="location specifier" optional="yes">
        <cocoa key="Location"/>
      </parameter>
      <parameter name="with data" code="data" description="The initial contents of the object." type="any" optional="yes">
        <cocoa key="ObjectData"/>
      </parameter>
      <parameter name="with properties" code="prdt" description="The initial values for properties of the object." type="record" optional="yes">
        <cocoa key="KeyDictionary"/>
      </parameter>
      <result description="to the new object" type="specifier"/>
    </command>

    <command name="move" code="coremove" description="Move object(s) to a new location.">
      <cocoa class="NSMoveCommand"/>
      <access-group identifier="*"/>
      <direct-parameter description="the object(s) to move" type="specifier"/>
      <parameter name="to" code="insh" description="The new location for the object(s)." type="location specifier">
        <cocoa key="ToLocation"/>
      </parameter>
      <result description="the moved object(s)" type="specifier"/>
    </command>

    <command name="print" code="aevtpdoc" description="Print an object.">
      <!-- type would be better written as "file | document". -->
      <direct-parameter description="The file(s) or document(s) to be printed." type="specifier"/>
    </command>

  </suite>

  <!--
    CHROMIUM SUITE
  -->
  <suite name="Chromium Suite" code="CrSu" description="Common classes and commands for Chrome.">

    <class-extension description="The application&apos;s top-level scripting object." extends="application">
      <cocoa class="BrowserCrApplication"/>
      <element description="Contains the bookmarks bar and other bookmarks folder." type="bookmark folder" access="r">
        <cocoa key="bookmarkFolders"/>
      </element>
      <property name="bookmarks bar" code="ChBB" description="The bookmarks bar bookmark folder." type="bookmark folder" access="r">
        <cocoa key="bookmarksBar"/>
      </property>
      <property name="other bookmarks" code="ChOB" description="The other bookmarks bookmark folder." type="bookmark folder" access="r">
        <cocoa key="otherBookmarks"/>
      </property>
    </class-extension>

    <class name="tab" code="CrTb" description="A tab.">
      <cocoa class="TabAppleScript"/>
      <property name="id" code="ID  " description="Unique ID of the tab." type="integer" access="r">
        <cocoa key="uniqueID"/>
      </property>
      <property name="title" code="pnam" description="The title of the tab." type="text" access="r"/>
      <property name="URL" code="URL " description="The url visible to the user." type="text"/>
      <property name="loading" code="ldng" description="Is loading?" type="boolean" access="r"/>
      <responds-to command="undo">
        <cocoa method="handlesUndoScriptCommand:"/>
      </responds-to>
      <responds-to command="redo">
        <cocoa method="handlesRedoScriptCommand:"/>
      </responds-to>
      <responds-to command="cut selection">
        <cocoa method="handlesCutScriptCommand:"/>
      </responds-to>
      <responds-to command="copy selection">
        <cocoa method="handlesCopyScriptCommand:"/>
      </responds-to>
      <responds-to command="paste selection">
        <cocoa method="handlesPasteScriptCommand:"/>
      </responds-to>
      <responds-to command="select all">
        <cocoa method="handlesSelectAllScriptCommand:"/>
      </responds-to>
      <responds-to command="go back">
        <cocoa method="handlesGoBackScriptCommand:"/>
      </responds-to>
      <responds-to command="go forward">
        <cocoa method="handlesGoForwardScriptCommand:"/>
      </responds-to>
      <responds-to command="reload">
        <cocoa method="handlesReloadScriptCommand:"/>
      </responds-to>
      <responds-to command="stop">
        <cocoa method="handlesStopScriptCommand:"/>
      </responds-to>
      <responds-to command="print">
        <cocoa method="handlesPrintScriptCommand:"/>
      </responds-to>
      <responds-to command="view source">
        <cocoa method="handlesViewSourceScriptCommand:"/>
      </responds-to>
      <responds-to command="save">
        <cocoa method="handlesSaveScriptCommand:"/>
      </responds-to>
      <responds-to command="close">
        <cocoa method="handlesCloseScriptCommand:"/>
      </responds-to>
      <responds-to command="execute">
        <cocoa method="handlesExecuteJavascriptScriptCommand:"/>
      </responds-to>
    </class>

    <class name="bookmark folder" code="CrBF" description="A bookmarks folder that contains other bookmarks folder and bookmark items.">
      <cocoa class="BookmarkFolderAppleScript"/>
      <element description="The bookmark folders present within." type="bookmark folder">
        <cocoa key="bookmarkFolders"/>
      </element>
      <element description="The bookmarks present within." type="bookmark item">
        <cocoa key="bookmarkItems"/>
      </element>
      <property name="id" code="ID  " description="Unique ID of the bookmark folder." type="number" access="r">
        <cocoa key="uniqueID"/>
      </property>
      <property name="title" code="pnam" description="The title of the folder." type="text"/>
      <property name="index" code="pidx" description="Returns the index with respect to its parent bookmark folder." type="number" access="r">
        <cocoa key="index"/>
      </property>
    </class>

    <class name="bookmark item" code="CrBI" description="An item consists of an URL and the title of a bookmark">
      <cocoa class="BookmarkItemAppleScript"/>
      <property name="id" code="ID  " description="Unique ID of the bookmark item." type="integer" access="r">
        <cocoa key="uniqueID"/>
      </property>
      <property name="title" code="pnam" description="The title of the bookmark item." type="text"/>
      <property name="URL" code="URL " description="The URL of the bookmark." type="text"/>
      <property name="index" code="pidx" description="Returns the index with respect to its parent bookmark folder." type="number" access="r">
        <cocoa key="index"/>
      </property>
    </class>

    <command name="reload" code="CrSuRlod" description="Reload a tab.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="go back" code="CrSuBack" description="Go Back (If Possible).">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="go forward" code="CrSuFwd " description="Go Forward (If Possible).">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="select all" code="CrSuSlAl" description="Select all.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="cut selection" code="CrSuCut " description="Cut selected text (If Possible).">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="copy selection" code="CrSuCop " description="Copy text.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="paste selection" code="CrSuPast" description="Paste text (If Possible).">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="undo" code="CrSuUndo" description="Undo the last change.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="redo" code="CrSuRedo" description="Redo the last change.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="stop" code="CrSustop" description="Stop the current tab from loading.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="view source" code="CrSuVSrc" description="View the HTML source of the tab.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
    </command>

    <command name="execute" code="CrSuExJa" description="Execute a piece of javascript.">
      <direct-parameter description="The tab to execute the command in." type="specifier"/>
      <parameter name="javascript" code="JvSc" description="The javascript code to execute." type="text">
        <cocoa key="javascript"/>
      </parameter>
      <result type="any"/>
    </command>

  </suite>

</dictionary>
