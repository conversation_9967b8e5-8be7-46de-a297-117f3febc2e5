CFBundleGetInfoString = "Chromium 101.0.4950.0, Autorių teisės priklauso „Chromium“ autoriams, 2022 m. Visos teisės saugomos.";
NSBluetoothAlwaysUsageDescription = "<PERSON> „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSBluetoothPeripheralUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSCameraUsageDescription = "<PERSON> „Chromium“ galės pasiekti duomenis, svetain<PERSON>s taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSHumanReadableCopyright = "Autorių teisės priklauso „Chromium“ autoriams, 2022 m. Visos teisės saugomos.";
NSLocationUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetain<PERSON>s taip pat galės prašyti suteikti leidimą juos pasiekti.";
NSMicrophoneUsageDescription = "Kai „Chromium“ galės pasiekti duomenis, svetainės taip pat galės prašyti suteikti leidimą juos pasiekti.";
