<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Chromium Helper (Alerts)</string>
	<key>CFBundleExecutable</key>
	<string>Chromium Helper (Alerts)</string>
	<key>CFBundleIconFile</key>
	<string>app.icns</string>
	<key>CFBundleIdentifier</key>
	<string>org.chromium.Chromium.framework.AlertNotificationService</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Chromium Helper (Alerts)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>101.0.4950.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>4950.0</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTSDKBuild</key>
	<string>12.3</string>
	<key>DTSDKName</key>
	<string>macosx12.3</string>
	<key>DTXcode</key>
	<string>1330</string>
	<key>DTXcodeBuild</key>
	<string>13E113</string>
	<key>LSEnvironment</key>
	<dict>
		<key>MallocNanoZone</key>
		<string>0</string>
	</dict>
	<key>LSFileQuarantineEnabled</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
	<string>10.11.0</string>
	<key>LSUIElement</key>
	<string>1</string>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>
	<key>SCMRevision</key>
	<string>4ac39669584a7fdc299ae09bdff846817bef293d-refs/heads/main@{#982053}</string>
</dict>
</plist>
