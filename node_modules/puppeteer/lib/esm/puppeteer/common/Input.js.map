{"version": 3, "file": "Input.js", "sourceRoot": "", "sources": ["../../../../src/common/Input.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC,OAAO,EAAE,cAAc,EAA2B,MAAM,uBAAuB,CAAC;AAQhF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAM,OAAO,QAAQ;IAMnB,gBAAgB;IAChB,YAAY,MAAkB;QAL9B,gBAAgB;QAChB,eAAU,GAAG,CAAC,CAAC;QACP,iBAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QAIvC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,KAAK,CAAC,IAAI,CACR,GAAa,EACb,UAA6B,EAAE,IAAI,EAAE,SAAS,EAAE;QAEhD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QAEvD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAEtD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QAC1E,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChD,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY;YACrC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,qBAAqB,EAAE,WAAW,CAAC,OAAO;YAC1C,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,IAAI,EAAE,IAAI;YACV,cAAc,EAAE,IAAI;YACpB,UAAU;YACV,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ,KAAK,CAAC;SACrC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,GAAW;QAC9B,IAAI,GAAG,KAAK,KAAK;YAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,GAAG,KAAK,SAAS;YAAE,OAAO,CAAC,CAAC;QAChC,IAAI,GAAG,KAAK,MAAM;YAAE,OAAO,CAAC,CAAC;QAC7B,IAAI,GAAG,KAAK,OAAO;YAAE,OAAO,CAAC,CAAC;QAC9B,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,wBAAwB,CAAC,SAAmB;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAClC,MAAM,WAAW,GAAG;YAClB,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,CAAC;YACV,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE,CAAC;SACZ,CAAC;QAEF,MAAM,UAAU,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAC7C,MAAM,CAAC,UAAU,EAAE,iBAAiB,SAAS,GAAG,CAAC,CAAC;QAElD,IAAI,UAAU,CAAC,GAAG;YAAE,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;QACrD,IAAI,KAAK,IAAI,UAAU,CAAC,QAAQ;YAAE,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC;QAExE,IAAI,UAAU,CAAC,OAAO;YAAE,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QACjE,IAAI,KAAK,IAAI,UAAU,CAAC,YAAY;YAClC,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC;QAEhD,IAAI,UAAU,CAAC,IAAI;YAAE,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAExD,IAAI,UAAU,CAAC,QAAQ;YAAE,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QAEpE,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC;QAErE,IAAI,UAAU,CAAC,IAAI;YAAE,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QACxD,IAAI,KAAK,IAAI,UAAU,CAAC,SAAS;YAAE,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;QAE3E,qEAAqE;QACrE,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YAAE,WAAW,CAAC,IAAI,GAAG,EAAE,CAAC;QAEhD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,EAAE,CAAC,GAAa;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QAEvD,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChD,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,qBAAqB,EAAE,WAAW,CAAC,OAAO;YAC1C,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;SAC/B,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,aAAa,CAAC,IAAY;QAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,OAAO,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,UAA8B,EAAE;QACvD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;YACvB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBACxB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;aACnC;iBAAM;gBACL,IAAI,KAAK;oBAAE,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC1D,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAChC;SACF;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,KAAK,CACT,GAAa,EACb,UAA6C,EAAE;QAE/C,MAAM,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACjC,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC9B,IAAI,KAAK;YAAE,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;CACF;AAuBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwDG;AACH,MAAM,OAAO,KAAK;IAOhB;;OAEG;IACH,YAAY,MAAkB,EAAE,QAAkB;QAP1C,OAAE,GAAG,CAAC,CAAC;QACP,OAAE,GAAG,CAAC,CAAC;QACP,YAAO,GAAyB,MAAM,CAAC;QAM7C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,IAAI,CACR,CAAS,EACT,CAAS,EACT,UAA8B,EAAE;QAEhC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,EACnB,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;QAClB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAClD,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC1C,CAAC,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;aACrC,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK,CACT,CAAS,EACT,CAAS,EACT,UAA6C,EAAE;QAE/C,MAAM,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACjC,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YAC/C,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;YACL,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,IAAI,CAAC,UAAwB,EAAE;QACnC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,cAAc;YACpB,MAAM;YACN,CAAC,EAAE,IAAI,CAAC,EAAE;YACV,CAAC,EAAE,IAAI,CAAC,EAAE;YACV,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACpC,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,EAAE,CAAC,UAAwB,EAAE;QACjC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,eAAe;YACrB,MAAM;YACN,CAAC,EAAE,IAAI,CAAC,EAAE;YACV,CAAC,EAAE,IAAI,CAAC,EAAE;YACV,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACpC,UAAU;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,KAAK,CAAC,UAA6B,EAAE;QACzC,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QAC3C,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,YAAY;YAClB,CAAC,EAAE,IAAI,CAAC,EAAE;YACV,CAAC,EAAE,IAAI,CAAC,EAAE;YACV,MAAM;YACN,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACpC,WAAW,EAAE,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,KAAY,EAAE,MAAa;QACpC,MAAM,OAAO,GAAG,IAAI,OAAO,CAA0B,CAAC,OAAO,EAAE,EAAE;YAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,KAAK,EAAE,EAAE,CACnD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CACpB,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,SAAS,CAAC,MAAa,EAAE,IAA6B;QAC1D,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjD,IAAI,EAAE,WAAW;YACjB,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACpC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAa,EAAE,IAA6B;QACzD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjD,IAAI,EAAE,UAAU;YAChB,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACpC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,MAAa,EAAE,IAA6B;QACrD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjD,IAAI,EAAE,MAAM;YACZ,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,CAAC,EAAE,MAAM,CAAC,CAAC;YACX,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;YACpC,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,WAAW,CACf,KAAY,EACZ,MAAa,EACb,UAA8B,EAAE;QAEhC,MAAM,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QACjC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC5C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnC,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;SAC5D;QACD,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9B,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;IAClB,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,WAAW;IAItB;;OAEG;IACH,YAAY,MAAkB,EAAE,QAAkB;QAChD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,GAAG,CAAC,CAAS,EAAE,CAAS;QAC5B,MAAM,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7D,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,YAAY;YAClB,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;SACrC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU;SACrC,CAAC,CAAC;IACL,CAAC;CACF"}