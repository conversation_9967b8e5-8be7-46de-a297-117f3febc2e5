{"version": 3, "file": "HTTPResponse.js", "sourceRoot": "", "sources": ["../../../../src/common/HTTPResponse.ts"], "names": [], "mappings": "AAoBA,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAEvD,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAiB5C;;;;;GAKG;AACH,MAAM,OAAO,YAAY;IAgBvB;;OAEG;IACH,YACE,MAAkB,EAClB,OAAoB,EACpB,eAA0C,EAC1C,SAAiE;QApB3D,oBAAe,GAA2B,IAAI,CAAC;QAS/C,aAAQ,GAA2B,EAAE,CAAC;QAa5C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,kBAAkB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAChD,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG;YACpB,EAAE,EAAE,eAAe,CAAC,eAAe;YACnC,IAAI,EAAE,eAAe,CAAC,UAAU;SACjC,CAAC;QACF,IAAI,CAAC,WAAW;YACd,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC;gBAC5C,eAAe,CAAC,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC;QACtD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAE9D,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC;QACzE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC;QACxE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAElD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,eAAe;YACrD,CAAC,CAAC,IAAI,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC;YACtD,CAAC,CAAC,IAAI,CAAC;QACT,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,4BAA4B,CAC1B,SAAiE;QAEjE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW;YAAE,OAAO;QACjD,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS;YAAE,OAAO;QACvB,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,UAAU;YAAE,OAAO;QACxB,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAiB;QAC5B,OAAO,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,EAAE;QACA,6BAA6B;QAC7B,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBAClE,IAAI,KAAK;oBAAE,MAAM,KAAK,CAAC;gBACvB,IAAI;oBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE;wBAClE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;qBACpC,CAAC,CAAC;oBACH,OAAO,MAAM,CAAC,IAAI,CAChB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAC3C,CAAC;iBACH;gBAAC,OAAO,KAAK,EAAE;oBACd,IACE,KAAK,YAAY,aAAa;wBAC9B,KAAK,CAAC,eAAe,KAAK,yCAAyC,EACnE;wBACA,MAAM,IAAI,aAAa,CACrB,gGAAgG,CACjG,CAAC;qBACH;oBAED,MAAM,KAAK,CAAC;iBACb;YACH,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACpC,OAAO,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF"}