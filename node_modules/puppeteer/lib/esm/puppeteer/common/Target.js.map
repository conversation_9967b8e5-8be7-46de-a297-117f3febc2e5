{"version": 3, "file": "Target.js", "sourceRoot": "", "sources": ["../../../../src/common/Target.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,IAAI,EAAqB,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAO3C;;GAEG;AACH,MAAM,OAAO,MAAM;IAmCjB;;OAEG;IACH,YACE,UAAsC,EACtC,cAA8B,EAC9B,cAAyC,EACzC,iBAA0B,EAC1B,eAAgC,EAChC,mBAA8B;QAE9B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,wCAAwC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,mCAAmC;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CACpC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,CACnD,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACvB,IAAI,CAAC,OAAO;gBAAE,OAAO,KAAK,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM;gBAC3D,OAAO,IAAI,CAAC;YACd,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC;YAC7C,IAAI,CAAC,UAAU,CAAC,aAAa,qBAAyB;gBAAE,OAAO,IAAI,CAAC;YACpE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpC,UAAU,CAAC,IAAI,sBAA0B,SAAS,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,OAAO,CACjC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,CAC9C,CAAC;QACF,IAAI,CAAC,cAAc;YACjB,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,EAAE,CAAC;QAClE,IAAI,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IACE,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM;YAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,iBAAiB;YAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC;YACtC,CAAC,IAAI,CAAC,YAAY,EAClB;YACA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACzD,IAAI,CAAC,MAAM,CACT,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,oBAAoB,CAC1B,CACF,CAAC;SACH;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IACE,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,gBAAgB;YAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,eAAe;YAEzC,OAAO,IAAI,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,yDAAyD;YACzD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAC/C,CAAC,MAAM,EAAE,EAAE,CACT,IAAI,SAAS,CACX,MAAM,EACN,IAAI,CAAC,WAAW,CAAC,GAAG,EACpB,GAAG,EAAE,GAAE,CAAC,CAAC,sBAAsB,EAC/B,GAAG,EAAE,GAAE,CAAC,CAAC,qBAAqB,CAC/B,CACJ,CAAC;SACH;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,IAAI;QAQF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACnC,IACE,IAAI,KAAK,MAAM;YACf,IAAI,KAAK,iBAAiB;YAC1B,IAAI,KAAK,gBAAgB;YACzB,IAAI,KAAK,eAAe;YACxB,IAAI,KAAK,SAAS;YAClB,IAAI,KAAK,SAAS;YAElB,OAAO,IAAI,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QACtC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAsC;QACvD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IACE,CAAC,IAAI,CAAC,cAAc;YACpB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,EAAE,CAAC,EACjE;YACA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO;SACR;IACH,CAAC;CACF"}