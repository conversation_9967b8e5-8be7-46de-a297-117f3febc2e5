{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/common/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAc,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAGtE,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAiC3C,MAAM,qCAAqC,GAAG,IAAI,GAAG,CAGnD;IACA,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,iCAAiC;IACjC,oBAAoB;IACpB,CAAC,QAAQ,EAAE,cAAc,CAAC;IAC1B,CAAC,YAAY,EAAE,cAAc,CAAC;IAC9B,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,sBAAsB,EAAE,SAAS,CAAC;IACnC,CAAC,eAAe,EAAE,SAAS,CAAC;IAC5B,CAAC,WAAW,EAAE,SAAS,CAAC;IACxB,CAAC,cAAc,EAAE,SAAS,CAAC;IAC3B,CAAC,sBAAsB,EAAE,qBAAqB,CAAC;IAC/C,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;IACxC,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;IACzC,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IACxC,CAAC,gBAAgB,EAAE,eAAe,CAAC;IACnC,uCAAuC;IACvC,CAAC,YAAY,EAAE,WAAW,CAAC;CAC5B,CAAC,CAAC;AAmFH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,MAAM,OAAO,OAAQ,SAAQ,YAAY;IAyCvC;;OAEG;IACH,YACE,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C;QAE3C,KAAK,EAAE,CAAC;QAnBF,oBAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QAoB1C,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,oBAAoB,GAAG,IAAI,SAAS,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,cAAmB,CAAC,CAAC;QAC5D,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,IAAI,CAAC,GAAY,EAAE,CAAC,IAAI,CAAC,CAAC;QAE3E,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,KAAK,MAAM,SAAS,IAAI,UAAU;YAChC,IAAI,CAAC,SAAS,CAAC,GAAG,CAChB,SAAS,EACT,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CACtD,CAAC;QAEJ,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,uBAAuB,CAAC,YAAY,EAAE,GAAG,EAAE,CAC7D,IAAI,CAAC,IAAI,mCAAmC,CAC7C,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,CAAC,EAAE,CACjB,wBAAwB,EACxB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,EAAE,CACjB,0BAA0B,EAC1B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CACnC,CAAC;IACJ,CAAC;IAlFD;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,MAAM,CACjB,UAAsB,EACtB,UAAoB,EACpB,iBAA0B,EAC1B,eAAiC,EACjC,OAAsB,EACtB,aAAoC,EACpC,oBAA2C;QAE3C,MAAM,OAAO,GAAG,IAAI,OAAO,CACzB,UAAU,EACV,UAAU,EACV,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,aAAa,EACb,oBAAoB,CACrB,CAAC;QACF,MAAM,UAAU,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QACvE,OAAO,OAAO,CAAC;IACjB,CAAC;IA6DD;;;OAGG;IACH,OAAO;;QACL,OAAO,MAAA,IAAI,CAAC,QAAQ,mCAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,6BAA6B,CACjC,UAAiC,EAAE;QAEnC,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QAEjD,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CACtD,6BAA6B,EAC7B;YACE,WAAW;YACX,eAAe,EAAE,eAAe,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;SAC9D,CACF,CAAC;QACF,MAAM,OAAO,GAAG,IAAI,cAAc,CAChC,IAAI,CAAC,WAAW,EAChB,IAAI,EACJ,gBAAgB,CACjB,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe,CAAC,SAAkB;QACtC,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1D,gBAAgB,EAAE,SAAS;SAC5B,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,KAAyC;;QAEzC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,MAAM,EAAE,gBAAgB,EAAE,GAAG,UAAU,CAAC;QACxC,MAAM,OAAO,GACX,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtD,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QAE3B,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,EAAE;YACzB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC9C,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAI,MAAM,CACvB,UAAU,EACV,OAAO,EACP,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,EAChD,IAAI,CAAC,kBAAkB,EACvB,MAAA,IAAI,CAAC,gBAAgB,mCAAI,IAAI,EAC7B,IAAI,CAAC,oBAAoB,CAC1B,CAAC;QACF,MAAM,CACJ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC7C,8CAA8C,CAC/C,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAErD,IAAI,MAAM,MAAM,CAAC,mBAAmB,EAAE;YACpC,IAAI,CAAC,IAAI,sCAAqC,MAAM,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,sCAA4C,MAAM,CAAC,CAAC;SACjE;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAA2B;QACxD,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC;YAAE,OAAO;QACrD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CACb,4CAA4C,KAAK,CAAC,QAAQ,GAAG,CAC9D,CAAC;SACH;QACD,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACrC,MAAM,CAAC,eAAe,EAAE,CAAC;QACzB,IAAI,MAAM,MAAM,CAAC,mBAAmB,EAAE;YACpC,IAAI,CAAC,IAAI,0CAAuC,MAAM,CAAC,CAAC;YACxD,MAAM;iBACH,cAAc,EAAE;iBAChB,IAAI,0CAA8C,MAAM,CAAC,CAAC;SAC9D;IACH,CAAC;IAEO,kBAAkB,CACxB,KAA6C;QAE7C,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CACb,6CAA6C,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG,CAC1E,CAAC;SACH;QACD,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;QACjC,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAC7C,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,cAAc,IAAI,WAAW,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE;YAClD,IAAI,CAAC,IAAI,sCAAqC,MAAM,CAAC,CAAC;YACtD,MAAM;iBACH,cAAc,EAAE;iBAChB,IAAI,sCAA4C,MAAM,CAAC,CAAC;SAC5D;IACH,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,oBAAoB,CAAC,SAAkB;QAC3C,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACtE,GAAG,EAAE,aAAa;YAClB,gBAAgB,EAAE,SAAS,IAAI,SAAS;SACzC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,GAAG,CAAC,CAAC;SAC/D;QACD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC;QACrD,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,0CAA0C,QAAQ,GAAG,CAAC,CAAC;SACxE;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CACb,6CAA6C,SAAS,GAAG,CAC1D,CAAC;SACH;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAC9C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,cAAc,CAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,SAAS,CACxC,CAAC;QACF,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,aAAa,CACjB,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QACpC,IAAI,OAAsD,CAAC;QAC3D,MAAM,aAAa,GAAG,IAAI,OAAO,CAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,EAAE,sCAAqC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,EAAE,sCAAqC,KAAK,CAAC,CAAC;QACnD,IAAI;YACF,IAAI,CAAC,OAAO;gBAAE,OAAO,MAAM,aAAa,CAAC;YACzC,OAAO,MAAM,MAAM,CAAC,eAAe,CACjC,OAAO,CAAC,IAAI,CAAC;gBACX,aAAa;gBACb,CAAC,KAAK,IAAI,EAAE;oBACV,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;wBACnC,IAAI,MAAM,SAAS,CAAC,MAAM,CAAC,EAAE;4BAC3B,OAAO,MAAM,CAAC;yBACf;qBACF;oBACD,MAAM,aAAa,CAAC;gBACtB,CAAC,CAAC,EAAE;aACL,CAAC,EACF,QAAQ,EACR,OAAO,CACR,CAAC;SACH;gBAAS;YACR,IAAI,CAAC,cAAc,sCAAqC,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,cAAc,sCAAqC,KAAK,CAAC,CAAC;SAChE;QAED,KAAK,UAAU,KAAK,CAAC,MAAc;YACjC,IAAI,MAAM,SAAS,CAAC,MAAM,CAAC;gBAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CACpC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CACzD,CAAC;QACF,iBAAiB;QACjB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,OAAO;QACX,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC,OAAO,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC,SAAS,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,UAAU;QACR,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACnC,CAAC;IAEO,WAAW;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACrD,CAAC;CACF;AA2BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,OAAO,cAAe,SAAQ,YAAY;IAK9C;;OAEG;IACH,YAAY,UAAsB,EAAE,OAAgB,EAAE,SAAkB;QACtE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ;aACjB,OAAO,EAAE;aACT,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,aAAa,CACX,SAAoD,EACpD,UAAgC,EAAE;QAElC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAChC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,EACjE,OAAO,CACR,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,IAAI,CAAC,OAAO,EAAE;aACX,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;aAC5C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAClC,CAAC;QACF,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;OAMG;IACH,WAAW;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,mBAAmB,CACvB,MAAc,EACd,WAAyB;QAEzB,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACzD,MAAM,kBAAkB,GACtB,qCAAqC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,kBAAkB;gBACrB,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,UAAU,CAAC,CAAC;YACvD,OAAO,kBAAkB,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,MAAM;YACN,gBAAgB,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;YACvC,WAAW,EAAE,mBAAmB;SACjC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,wBAAwB;QAC5B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtD,gBAAgB,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;SACxC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAAC;QAC7D,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;CACF"}