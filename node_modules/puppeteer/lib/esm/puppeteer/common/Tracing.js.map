{"version": 3, "file": "Tracing.js", "sourceRoot": "", "sources": ["../../../../src/common/Tracing.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAYrC;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,OAAO;IAKlB;;OAEG;IACH,YAAY,MAAkB;QAN9B,eAAU,GAAG,KAAK,CAAC;QACnB,UAAK,GAAG,EAAE,CAAC;QAMT,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK,CAAC,UAA0B,EAAE;QACtC,MAAM,CACJ,CAAC,IAAI,CAAC,UAAU,EAChB,6DAA6D,CAC9D,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACxB,IAAI;YACJ,mBAAmB;YACnB,YAAY;YACZ,uCAAuC;YACvC,6CAA6C;YAC7C,UAAU;YACV,eAAe;YACf,mBAAmB;YACnB,aAAa;YACb,6CAA6C;YAC7C,qCAAqC;SACtC,CAAC;QACF,MAAM,EACJ,IAAI,GAAG,IAAI,EACX,WAAW,GAAG,KAAK,EACnB,UAAU,GAAG,iBAAiB,GAC/B,GAAG,OAAO,CAAC;QAEZ,IAAI,WAAW;YAAE,UAAU,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAE5E,MAAM,kBAAkB,GAAG,UAAU;aAClC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aACpC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,kBAAkB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAE5E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;YACvC,YAAY,EAAE,gBAAgB;YAC9B,WAAW,EAAE;gBACX,kBAAkB;gBAClB,kBAAkB;aACnB;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,OAAgC,CAAC;QACrC,IAAI,MAA4B,CAAC;QACjC,MAAM,cAAc,GAAG,IAAI,OAAO,CAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClD,OAAO,GAAG,CAAC,CAAC;YACZ,MAAM,GAAG,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YAC3D,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,6BAA6B,CACzD,IAAI,CAAC,OAAO,EACZ,KAAK,CAAC,MAAM,CACb,CAAC;gBACF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtE,OAAO,CAAC,MAAM,CAAC,CAAC;aACjB;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,CAAC,KAAK,CAAC,CAAC;aACf;QACH,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,cAAc,CAAC;IACxB,CAAC;CACF"}