{"version": 3, "file": "Coverage.js", "sourceRoot": "", "sources": ["../../../../src/common/Coverage.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,UAAU,EAA0B,MAAM,aAAa,CAAC;AAIzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAmE9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,MAAM,OAAO,QAAQ;IAUnB,YAAY,MAAkB;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,eAAe,CAAC,UAA6B,EAAE;QACnD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,cAAc;QAClB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,gBAAgB,CAAC,UAA8B,EAAE;QACrD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,eAAe;QACnB,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,UAAU;IAUrB,YAAY,MAAkB;QAR9B,aAAQ,GAAG,KAAK,CAAC;QACjB,gBAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;QACxC,mBAAc,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC3C,oBAAe,GAA6B,EAAE,CAAC;QAC/C,uBAAkB,GAAG,KAAK,CAAC;QAC3B,4BAAuB,GAAG,KAAK,CAAC;QAChC,8BAAyB,GAAG,KAAK,CAAC;QAGhC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,KAAK,CACT,UAII,EAAE;QAEN,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,+BAA+B,CAAC,CAAC;QACxD,MAAM,EACJ,iBAAiB,GAAG,IAAI,EACxB,sBAAsB,GAAG,KAAK,EAC9B,wBAAwB,GAAG,KAAK,GACjC,GAAG,OAAO,CAAC;QACZ,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG;YACrB,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,OAAO,EACZ,uBAAuB,EACvB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,OAAO,EACZ,kCAAkC,EAClC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C;SACF,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBACjD,SAAS,EAAE,IAAI,CAAC,yBAAyB;gBACzC,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;SAC/D,CAAC,CAAC;IACL,CAAC;IAED,2BAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO;QACrC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,KAA0C;QAE1C,oCAAoC;QACpC,IAAI,KAAK,CAAC,GAAG,KAAK,qBAAqB;YAAE,OAAO;QAChD,mFAAmF;QACnF,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,uBAAuB;YAAE,OAAO;QACxD,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACnE,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;SAChE;QAAC,OAAO,KAAK,EAAE;YACd,4DAA4D;YAC5D,UAAU,CAAC,KAAK,CAAC,CAAC;SACnB;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC;YACjD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC;YACjD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC;SACtC,CAAC,CAAC;QAEH,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAElD,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAElC,KAAK,MAAM,KAAK,IAAI,eAAe,CAAC,MAAM,EAAE;YAC1C,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,uBAAuB;gBACtC,GAAG,GAAG,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC;YACzC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,IAAI,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS;gBAAE,SAAS;YACtD,MAAM,aAAa,GAAG,EAAE,CAAC;YACzB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,SAAS;gBAAE,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,MAAM,MAAM,GAAG,uBAAuB,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;aACtC;iBAAM;gBACL,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC;aAChE;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,WAAW;IAStB,YAAY,MAAkB;QAP9B,aAAQ,GAAG,KAAK,CAAC;QACjB,oBAAe,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC5C,uBAAkB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC/C,oBAAe,GAA6B,EAAE,CAAC;QAC/C,uBAAkB,GAAG,KAAK,CAAC;QAC3B,4BAAuB,GAAG,KAAK,CAAC;QAG9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,UAA2C,EAAE;QACvD,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,gCAAgC,CAAC,CAAC;QACzD,MAAM,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QAC7C,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG;YACrB,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,OAAO,EACZ,qBAAqB,EACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,OAAO,EACZ,kCAAkC,EAClC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5C;SACF,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC;SAChD,CAAC,CAAC;IACL,CAAC;IAED,2BAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAAE,OAAO;QACrC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAwC;QAC1D,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,SAAS;YAAE,OAAO;QAC9B,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAChE,YAAY,EAAE,MAAM,CAAC,YAAY;aAClC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;SACjE;QAAC,OAAO,KAAK,EAAE;YACd,4DAA4D;YAC5D,UAAU,CAAC,KAAK,CAAC,CAAC;SACnB;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,4BAA4B,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAClD,2BAA2B,CAC5B,CAAC;QACF,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;SACjC,CAAC,CAAC;QACH,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAElD,4BAA4B;QAC5B,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QACzC,KAAK,MAAM,KAAK,IAAI,oBAAoB,CAAC,SAAS,EAAE;YAClD,IAAI,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,GAAG,EAAE,CAAC;gBACZ,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;aACxD;YACD,MAAM,CAAC,IAAI,CAAC;gBACV,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1B,CAAC,CAAC;SACJ;QAED,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;YACtD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,uBAAuB,CACpC,sBAAsB,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAC/C,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;SACtC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,SAAS,uBAAuB,CAC9B,YAA8E;IAE9E,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE;QAChC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;KAC1D;IACD,oDAAoD;IACpD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACnB,gCAAgC;QAChC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;YAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;QACtD,oDAAoD;QACpD,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;YAAE,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QAC9C,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;QACxD,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;QACxD,gEAAgE;QAChE,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC;YAAE,OAAO,OAAO,GAAG,OAAO,CAAC;QAC3C,+DAA+D;QAC/D,OAAO,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,6CAA6C;IAC7C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,IACE,aAAa,CAAC,MAAM;YACpB,UAAU,GAAG,KAAK,CAAC,MAAM;YACzB,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAC3C;YACA,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvE,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,KAAK,UAAU;gBAC7C,UAAU,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;;gBAC3B,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;SAC7D;QACD,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;YAAE,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;YACvD,aAAa,CAAC,GAAG,EAAE,CAAC;KAC1B;IACD,2BAA2B;IAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAChE,CAAC"}