{"version": 3, "file": "LifecycleWatcher.js", "sourceRoot": "", "sources": ["../../../../src/common/LifecycleWatcher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAA0B,MAAM,aAAa,CAAC;AAC7D,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,EAGL,yBAAyB,GAC1B,MAAM,mBAAmB,CAAC;AAG3B,OAAO,EAAE,2BAA2B,EAAE,MAAM,qBAAqB,CAAC;AAClE,OAAO,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAmB1D,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAG1C;IACA,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,cAAc,EAAE,aAAa,CAAC;IAC/B,CAAC,cAAc,EAAE,mBAAmB,CAAC;CACtC,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,OAAO,gBAAgB;IA2B3B,YACE,YAA0B,EAC1B,KAAY,EACZ,SAA8D,EAC9D,OAAe;QAEf,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;YAAE,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;aACvD,IAAI,OAAO,SAAS,KAAK,QAAQ;YAAE,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAChD,MAAM,aAAa,GAAG,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9D,MAAM,CAAC,aAAa,EAAE,uCAAuC,GAAG,KAAK,CAAC,CAAC;YACvE,OAAO,aAAa,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG;YACrB,MAAM,CAAC,gBAAgB,CACrB,YAAY,CAAC,OAAO,EACpB,uBAAuB,CAAC,YAAY,EACpC,GAAG,EAAE,CACH,IAAI,CAAC,UAAU,CACb,IAAI,KAAK,CAAC,qDAAqD,CAAC,CACjE,CACJ;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,aAAa,EAClB,yBAAyB,CAAC,cAAc,EACxC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,aAAa,EAClB,yBAAyB,CAAC,4BAA4B,EACtD,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CACzC;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,aAAa,EAClB,yBAAyB,CAAC,YAAY,EACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,aAAa,EAClB,yBAAyB,CAAC,aAAa,EACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC;YACD,MAAM,CAAC,gBAAgB,CACrB,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,EACnC,2BAA2B,CAAC,OAAO,EACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B;SACF,CAAC;QAEF,IAAI,CAAC,8BAA8B,GAAG,IAAI,OAAO,CAC/C,CAAC,OAAO,EAAE,EAAE;YACV,IAAI,CAAC,uCAAuC,GAAG,OAAO,CAAC;QACzD,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC/C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,6BAA6B,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3D,IAAI,CAAC,sCAAsC,GAAG,OAAO,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACpD,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,UAAU,CAAC,OAAoB;QAC7B,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;YACnE,OAAO;QACT,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACpC,CAAC;IAED,gBAAgB,CAAC,KAAY;QAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACzB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAC5B,IAAI,EACJ,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAC3C,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,2EAA2E;QAC3E,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED,UAAU,CAAC,KAAY;QACrB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,6BAA6B;QAC3B,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC7C,CAAC;IAED,4BAA4B;QAC1B,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC5C,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAED,2BAA2B;QACzB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,qBAAqB;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACjD,MAAM,YAAY,GAChB,wBAAwB,GAAG,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QAC5D,OAAO,IAAI,OAAO,CAChB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CACvE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,wBAAwB,CAAC,KAAY;QACnC,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM;YAAE,OAAO;QAClC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM;YAAE,OAAO;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,uBAAuB;QACrB,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC;YAAE,OAAO;QAClE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IACE,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB;YAC/C,CAAC,IAAI,CAAC,0BAA0B,EAChC;YACA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,CAAC,sCAAsC,EAAE,CAAC;aAC/C;YACD,OAAO;SACR;QACD,IAAI,IAAI,CAAC,0BAA0B;YACjC,IAAI,CAAC,uCAAuC,EAAE,CAAC;QACjD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB;YACjD,IAAI,CAAC,sCAAsC,EAAE,CAAC;QAEhD;;;;WAIG;QACH,SAAS,cAAc,CACrB,KAAY,EACZ,iBAA2C;YAE3C,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE;gBACrC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC;oBAAE,OAAO,KAAK,CAAC;aACtD;YACD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;gBACvC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC;oBAAE,OAAO,KAAK,CAAC;aAC7D;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO;QACL,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;CACF"}