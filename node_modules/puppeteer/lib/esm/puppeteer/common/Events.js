/**
 * Copyright 2019 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * IMPORTANT: we are mid-way through migrating away from this Events.ts file
 * in favour of defining events next to the class that emits them.
 *
 * However we need to maintain this file for now because the legacy DocLint
 * system relies on them. Be aware in the mean time if you make a change here
 * you probably need to replicate it in the relevant class. For example if you
 * add a new Page event, you should update the PageEmittedEvents enum in
 * src/common/Page.ts.
 *
 * Chat to @jackfranklin if you're unsure.
 */
export const Events = {
    Page: {
        Close: 'close',
        Console: 'console',
        Dialog: 'dialog',
        DOMContentLoaded: 'domcontentloaded',
        Error: 'error',
        // Can't use just 'error' due to node.js special treatment of error events.
        // @see https://nodejs.org/api/events.html#events_error_events
        PageError: 'pageerror',
        Request: 'request',
        Response: 'response',
        RequestFailed: 'requestfailed',
        RequestFinished: 'requestfinished',
        FrameAttached: 'frameattached',
        FrameDetached: 'framedetached',
        FrameNavigated: 'framenavigated',
        Load: 'load',
        Metrics: 'metrics',
        Popup: 'popup',
        WorkerCreated: 'workercreated',
        WorkerDestroyed: 'workerdestroyed',
    },
    Browser: {
        TargetCreated: 'targetcreated',
        TargetDestroyed: 'targetdestroyed',
        TargetChanged: 'targetchanged',
        Disconnected: 'disconnected',
    },
    BrowserContext: {
        TargetCreated: 'targetcreated',
        TargetDestroyed: 'targetdestroyed',
        TargetChanged: 'targetchanged',
    },
    NetworkManager: {
        Request: Symbol('Events.NetworkManager.Request'),
        Response: Symbol('Events.NetworkManager.Response'),
        RequestFailed: Symbol('Events.NetworkManager.RequestFailed'),
        RequestFinished: Symbol('Events.NetworkManager.RequestFinished'),
    },
    FrameManager: {
        FrameAttached: Symbol('Events.FrameManager.FrameAttached'),
        FrameNavigated: Symbol('Events.FrameManager.FrameNavigated'),
        FrameDetached: Symbol('Events.FrameManager.FrameDetached'),
        LifecycleEvent: Symbol('Events.FrameManager.LifecycleEvent'),
        FrameNavigatedWithinDocument: Symbol('Events.FrameManager.FrameNavigatedWithinDocument'),
        ExecutionContextCreated: Symbol('Events.FrameManager.ExecutionContextCreated'),
        ExecutionContextDestroyed: Symbol('Events.FrameManager.ExecutionContextDestroyed'),
    },
    Connection: {
        Disconnected: Symbol('Events.Connection.Disconnected'),
    },
    CDPSession: {
        Disconnected: Symbol('Events.CDPSession.Disconnected'),
    },
};
//# sourceMappingURL=Events.js.map