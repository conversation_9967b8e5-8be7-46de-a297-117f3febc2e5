{"version": 3, "file": "DOMWorld.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/DOMWorld.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAIH,OAAO,EAEL,uBAAuB,EACxB,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,EACL,sBAAsB,EACtB,gBAAgB,EAChB,iBAAiB,EACjB,UAAU,EACV,oBAAoB,EACpB,iBAAiB,EAClB,MAAM,gBAAgB,CAAC;AAGxB,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAc7C;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,aAAa,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,QAAQ,CAAC;CACxB;AAED;;GAEG;AACH,qBAAa,QAAQ;IACnB,OAAO,CAAC,aAAa,CAAe;IACpC,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,MAAM,CAAQ;IACtB,OAAO,CAAC,gBAAgB,CAAkB;IAC1C,OAAO,CAAC,gBAAgB,CAAC,CAAgC;IACzD,OAAO,CAAC,eAAe,CAAC,CAAmC;IAE3D,OAAO,CAAC,uBAAuB,CAAC,CAAwC;IAExE,OAAO,CAAC,SAAS,CAAS;IAC1B;;OAEG;IACH,UAAU,gBAAuB;IAEjC;;;OAGG;IACH,eAAe,wBAA+B;IAE9C,OAAO,CAAC,YAAY,CAAqB;IACzC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CACP;gBAGvB,MAAM,EAAE,UAAU,EAClB,YAAY,EAAE,YAAY,EAC1B,KAAK,EAAE,KAAK,EACZ,eAAe,EAAE,eAAe;IAalC,KAAK,IAAI,KAAK;IAIR,WAAW,CAAC,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IAkB5D,WAAW,IAAI,OAAO;IAItB,OAAO,IAAI,IAAI;IASf,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAQvC,cAAc,CAAC,WAAW,SAAS,QAAQ,GAAG,QAAQ,EAC1D,YAAY,EAAE,gBAAgB,EAC9B,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,WAAW,CAAC;IAKjB,QAAQ,CAAC,CAAC,SAAS,UAAU,EACjC,YAAY,EAAE,CAAC,EACf,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;IAQhD,CAAC,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EACjC,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAM7B,SAAS,IAAI,OAAO,CAAC,aAAa,CAAC;IASnC,EAAE,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAMhD,KAAK,CAAC,UAAU,EACpB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,CACZ,OAAO,EAAE,OAAO,EAChB,GAAG,IAAI,EAAE,OAAO,EAAE,KACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAKnC,MAAM,CAAC,UAAU,EACrB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,CACZ,QAAQ,EAAE,OAAO,EAAE,EACnB,GAAG,IAAI,EAAE,OAAO,EAAE,KACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAUnC,EAAE,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAClC,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAM7B,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAW1B,UAAU,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,IAAI,CAAC;IA0BhB;;;;;;;;OAQG;IACG,YAAY,CAAC,OAAO,EAAE;QAC1B,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,EAAE,CAAC,EAAE,MAAM,CAAC;QACZ,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,GAAG,OAAO,CAAC,aAAa,CAAC;IAgF1B;;;;;;;;;OASG;IACG,WAAW,CAAC,OAAO,EAAE;QACzB,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,GAAG,OAAO,CAAC,aAAa,CAAC;IAgEpB,KAAK,CACT,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,WAAW,CAAC;QAAC,UAAU,CAAC,EAAE,MAAM,CAAA;KAAE,GACrE,OAAO,CAAC,IAAI,CAAC;IAOV,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAOtC,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAOtC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAQhE,GAAG,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAMpC,IAAI,CACR,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAC1B,OAAO,CAAC,IAAI,CAAC;IAOV,eAAe,CACnB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,sBAAsB,GAC9B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAQhC,OAAO,CAAC,iBAAiB,CAA8B;IACvD;;OAEG;IACG,mBAAmB,CACvB,OAAO,EAAE,gBAAgB,EACzB,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,IAAI,CAAC;YAqDF,gBAAgB;IAwC9B;;OAEG;IACG,qBAAqB,CACzB,QAAQ,EAAE,QAAQ,EAClB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,sBAAsB,EAC/B,OAAO,CAAC,EAAE,WAAW,GACpB,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IA0C1B,YAAY,CAChB,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,sBAAsB,GAC9B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IA2ChC,eAAe,CACb,YAAY,EAAE,QAAQ,GAAG,MAAM,EAC/B,OAAO,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO,EAC7D,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,QAAQ,CAAC;IAgBd,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;CAG/B;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B,QAAQ,EAAE,QAAQ,CAAC;IACnB,aAAa,EAAE,QAAQ,GAAG,MAAM,CAAC;IACjC,8BAA8B,EAAE,OAAO,CAAC;IACxC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,WAAW,CAAC;IACtB,IAAI,EAAE,sBAAsB,EAAE,CAAC;IAC/B,IAAI,CAAC,EAAE,aAAa,CAAC;CACtB;AAED;;GAEG;AACH,qBAAa,QAAQ;IACnB,SAAS,EAAE,QAAQ,CAAC;IACpB,QAAQ,EAAE,MAAM,GAAG,MAAM,CAAC;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,MAAM,CAAC;IACvB,+BAA+B,EAAE,OAAO,CAAC;IACzC,KAAK,EAAE,sBAAsB,EAAE,CAAC;IAChC,QAAQ,EAAE,WAAW,CAAC;IACtB,SAAS,SAAK;IACd,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC3B,QAAQ,EAAE,CAAC,CAAC,EAAE,QAAQ,KAAK,IAAI,CAAC;IAChC,OAAO,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;IAC5B,aAAa,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;IAC/B,WAAW,UAAS;IACpB,KAAK,EAAE,aAAa,CAAQ;gBAEhB,OAAO,EAAE,eAAe;IAqDpC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAMvB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAyE5B,QAAQ,IAAI,IAAI;CAIjB"}