{"version": 3, "file": "ExecutionContext.js", "sourceRoot": "", "sources": ["../../../../src/common/ExecutionContext.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAiB,MAAM,eAAe,CAAC;AAMxE;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,iCAAiC,CAAC;AACvE,MAAM,gBAAgB,GAAG,6CAA6C,CAAC;AAEvE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,OAAO,gBAAgB;IAkB3B;;OAEG;IACH,YACE,MAAkB,EAClB,cAA4D,EAC5D,KAAe;QAEf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;IAC1C,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAClD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA0CG;IACH,KAAK,CAAC,QAAQ,CACZ,YAA+B,EAC/B,GAAG,IAAe;QAElB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CACjC,IAAI,EACJ,YAAY,EACZ,GAAG,IAAI,CACR,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyCG;IACH,KAAK,CAAC,cAAc,CAClB,YAA8B,EAC9B,GAAG,IAA8B;QAEjC,OAAO,IAAI,CAAC,iBAAiB,CAAa,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,aAAsB,EACtB,YAA+B,EAC/B,GAAG,IAAe;QAElB,MAAM,MAAM,GAAG,iBAAiB,qBAAqB,EAAE,CAAC;QAExD,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YAClC,MAAM,UAAU,GAAG,YAAY,CAAC;YAChC,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/D,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,UAAU,GAAG,IAAI,GAAG,MAAM,CAAC;YAE/B,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO;iBAClE,IAAI,CAAC,kBAAkB,EAAE;gBACxB,UAAU,EAAE,uBAAuB;gBACnC,SAAS;gBACT,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;YAEvB,IAAI,gBAAgB;gBAClB,MAAM,IAAI,KAAK,CACb,qBAAqB,GAAG,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CACrE,CAAC;YAEJ,OAAO,aAAa;gBAClB,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,YAAY,CAAC;gBAC5C,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;SACxC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU;YACpC,MAAM,IAAI,KAAK,CACb,0EAA0E,YAAY,YAAY,CACnG,CAAC;QAEJ,IAAI,YAAY,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC3C,IAAI;YACF,IAAI,QAAQ,CAAC,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;YACd,6DAA6D;YAC7D,8BAA8B;YAC9B,IAAI,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACnC,YAAY;oBACV,iBAAiB,GAAG,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;;gBAC3D,YAAY,GAAG,WAAW,GAAG,YAAY,CAAC;YAC/C,IAAI;gBACF,IAAI,QAAQ,CAAC,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;aACxC;YAAC,OAAO,KAAK,EAAE;gBACd,8DAA8D;gBAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;aAC9D;SACF;QACD,IAAI,qBAAqB,CAAC;QAC1B,IAAI;YACF,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAClE,mBAAmB,EAAE,YAAY,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI;gBACxD,kBAAkB,EAAE,IAAI,CAAC,UAAU;gBACnC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/C,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,IACE,KAAK,YAAY,SAAS;gBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC;gBAEjE,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;YACzD,MAAM,KAAK,CAAC;SACb;QACD,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAE,GAC9C,MAAM,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,gBAAgB;YAClB,MAAM,IAAI,KAAK,CACb,qBAAqB,GAAG,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CACrE,CAAC;QACJ,OAAO,aAAa;YAClB,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,YAAY,CAAC;YAC5C,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAEvC;;;;WAIG;QACH,SAAS,eAAe,CAAyB,GAAY;YAC3D,IAAI,OAAO,GAAG,KAAK,QAAQ;gBACzB,mCAAmC;gBACnC,OAAO,EAAE,mBAAmB,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;YACvD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAAE,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC;YAC7D,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC;gBAAE,OAAO,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC;YACzE,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;gBAC3B,OAAO,EAAE,mBAAmB,EAAE,WAAW,EAAE,CAAC;YAC9C,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;gBAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC;YAC/D,MAAM,YAAY,GAAG,GAAG,IAAI,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;YACjE,IAAI,YAAY,EAAE;gBAChB,IAAI,YAAY,CAAC,QAAQ,KAAK,IAAI;oBAChC,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;gBACJ,IAAI,YAAY,CAAC,SAAS;oBAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBACrE,IAAI,YAAY,CAAC,aAAa,CAAC,mBAAmB;oBAChD,OAAO;wBACL,mBAAmB,EAAE,YAAY,CAAC,aAAa,CAAC,mBAAmB;qBACpE,CAAC;gBACJ,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,QAAQ;oBACtC,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gBACrD,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;aAC1D;YACD,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QACxB,CAAC;QAED,SAAS,YAAY,CAAC,KAAY;YAChC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAC;gBAC9D,OAAO,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,CAAC;YAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBAChE,OAAO,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,CAAC;YAE3C,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC;gBAC/D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC;gBAE9D,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;YACJ,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,YAAY,CAAC,eAAyB;QAC1C,MAAM,CAAC,CAAC,eAAe,CAAC,SAAS,EAAE,iCAAiC,CAAC,CAAC;QACtE,MAAM,CACJ,eAAe,CAAC,aAAa,CAAC,QAAQ,EACtC,4DAA4D,CAC7D,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC/D,iBAAiB,EAAE,eAAe,CAAC,aAAa,CAAC,QAAQ;SAC1D,CAAC,CAAC;QACH,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,aAAyC;QAEzC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC5D,aAAa,EAAE,aAAa;YAC5B,kBAAkB,EAAE,IAAI,CAAC,UAAU;SACpC,CAAC,CAAC;QACH,OAAO,cAAc,CAAC,IAAI,EAAE,MAAM,CAAkB,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,aAA4B;QAE5B,MAAM,CACJ,aAAa,CAAC,gBAAgB,EAAE,KAAK,IAAI,EACzC,oEAAoE,CACrE,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,sCAAsC,CAAC,CAAC;QAC5D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC3D,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,QAAQ;SAC/C,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/D,CAAC;CACF"}