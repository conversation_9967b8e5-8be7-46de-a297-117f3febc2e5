{"version": 3, "file": "LifecycleWatcher.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/LifecycleWatcher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAGH,OAAO,EAAU,sBAAsB,EAAE,MAAM,aAAa,CAAC;AAC7D,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,EACL,YAAY,EACZ,KAAK,EAEN,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAGjD;;GAEG;AACH,oBAAY,uBAAuB,GAC/B,MAAM,GACN,kBAAkB,GAClB,cAAc,GACd,cAAc,CAAC;AAEnB;;GAEG;AACH,oBAAY,sBAAsB,GAC9B,MAAM,GACN,kBAAkB,GAClB,aAAa,GACb,mBAAmB,CAAC;AAYxB;;GAEG;AACH,qBAAa,gBAAgB;IAC3B,kBAAkB,EAAE,sBAAsB,EAAE,CAAC;IAC7C,aAAa,EAAE,YAAY,CAAC;IAC5B,MAAM,EAAE,KAAK,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,kBAAkB,CAAC,EAAE,WAAW,CAAC;IACjC,eAAe,EAAE,sBAAsB,EAAE,CAAC;IAC1C,gBAAgB,EAAE,MAAM,CAAC;IAEzB,8BAA8B,EAAE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;IACtD,uCAAuC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;IAE7D,iBAAiB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,kBAAkB,EAAE,MAAM,IAAI,CAAC;IAE/B,6BAA6B,EAAE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;IACrD,sCAAsC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;IAE5D,mBAAmB,EAAE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;IAC3C,oBAAoB,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC;IAE1C,eAAe,EAAE,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;IAE9C,aAAa,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;IAC/B,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC,QAAQ,CAAC,EAAE,OAAO,CAAC;gBAGjB,YAAY,EAAE,YAAY,EAC1B,KAAK,EAAE,KAAK,EACZ,SAAS,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,EAC9D,OAAO,EAAE,MAAM;IAwEjB,UAAU,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI;IAMtC,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAW9B,kBAAkB,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAKxD,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAI9B,6BAA6B,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IAItD,4BAA4B,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IAIrD,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC,2BAA2B,IAAI,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,IAAI,CAAC;IAInE,qBAAqB,IAAI,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IASrD,wBAAwB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAM5C,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAMjC,uBAAuB,IAAI,IAAI;IAsC/B,OAAO,IAAI,IAAI;CAIhB"}