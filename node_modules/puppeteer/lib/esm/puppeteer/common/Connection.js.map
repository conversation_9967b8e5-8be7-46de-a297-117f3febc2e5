{"version": 3, "file": "Connection.js", "sourceRoot": "", "sources": ["../../../../src/common/Connection.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,MAAM,iBAAiB,GAAG,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC7D,MAAM,oBAAoB,GAAG,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAKhE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAiB5C;;;;GAIG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,YAAY,EAAE,MAAM,CAAC,yBAAyB,CAAC;CACvC,CAAC;AAEX;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,YAAY;IAU1C,YAAY,GAAW,EAAE,SAA8B,EAAE,KAAK,GAAG,CAAC;QAChE,KAAK,EAAE,CAAC;QAPV,YAAO,GAAG,CAAC,CAAC;QACZ,cAAS,GAA4B,IAAI,GAAG,EAAE,CAAC;QAC/C,YAAO,GAAG,KAAK,CAAC;QAEhB,eAAU,GAAoC,IAAI,GAAG,EAAE,CAAC;QAItD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAmB;QACpC,OAAO,OAAO,CAAC,WAAW,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,SAAiB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,CACF,MAAS,EACT,GAAG,SAAoD;QAEvD,2EAA2E;QAC3E,0CAA0C;QAC1C,sFAAsF;QACtF,yEAAyE;QACzE,kBAAkB;QAClB,iFAAiF;QACjF,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC3D,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE;gBACtB,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,IAAI,aAAa,EAAE;gBAC1B,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CAAC,OAAgC;QACvC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC;QAC1B,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CACvC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,CACnC,CAAC;QACF,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QACtE,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,MAAM,CAAC,MAAM,KAAK,yBAAyB,EAAE;YAC/C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,UAAU,CAC5B,IAAI,EACJ,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAC7B,SAAS,CACV,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3D,IAAI,aAAa,EAAE;gBACjB,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;aAChD;SACF;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,2BAA2B,EAAE;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC5D,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBACtC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC3D,IAAI,aAAa,EAAE;oBACjB,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;iBAChD;aACF;SACF;QACD,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,OAAO;gBAAE,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;SACzC;aAAM,IAAI,MAAM,CAAC,EAAE,EAAE;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChD,sEAAsE;YACtE,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAClC,IAAI,MAAM,CAAC,KAAK;oBACd,QAAQ,CAAC,MAAM,CACb,mBAAmB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7D,CAAC;;oBACC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACtC;SACF;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACzC;IACH,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;QACpC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC7C,QAAQ,CAAC,MAAM,CACb,YAAY,CACV,QAAQ,CAAC,KAAK,EACd,mBAAmB,QAAQ,CAAC,MAAM,mBAAmB,CACtD,CACF,CAAC;QACJ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAAE,OAAO,CAAC,SAAS,EAAE,CAAC;QACnE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED,OAAO;QACL,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CACjB,UAAsC;QAEtC,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC7D,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AAaD;;;;GAIG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,YAAY,EAAE,MAAM,CAAC,yBAAyB,CAAC;CACvC,CAAC;AAEX;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,OAAO,UAAW,SAAQ,YAAY;IAS1C;;OAEG;IACH,YAAY,UAAsB,EAAE,UAAkB,EAAE,SAAiB;QACvE,KAAK,EAAE,CAAC;QANF,eAAU,GAAoC,IAAI,GAAG,EAAE,CAAC;QAO9D,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,IAAI,CACF,MAAS,EACT,GAAG,SAAoD;QAEvD,IAAI,CAAC,IAAI,CAAC,WAAW;YACnB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CACP,mBAAmB,MAAM,sCAAsC,IAAI,CAAC,WAAW,mBAAmB,CACnG,CACF,CAAC;QAEJ,gEAAgE;QAChE,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE3D,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;YACnC,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,MAAM;YACN,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE;gBACtB,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,IAAI,aAAa,EAAE;gBAC1B,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,MAAiC;QAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,IAAI,MAAM,CAAC,EAAE,IAAI,QAAQ,EAAE;YACzB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,MAAM,CAAC,KAAK;gBACd,QAAQ,CAAC,MAAM,CACb,mBAAmB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7D,CAAC;;gBACC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACtC;aAAM;YACL,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACzC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,WAAW;YACnB,MAAM,IAAI,KAAK,CACb,6CAA6C,IAAI,CAAC,WAAW,mBAAmB,CACjF,CAAC;QACJ,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACrD,SAAS,EAAE,IAAI,CAAC,UAAU;SAC3B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS;QACP,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC7C,QAAQ,CAAC,MAAM,CACb,YAAY,CACV,QAAQ,CAAC,KAAK,EACd,mBAAmB,QAAQ,CAAC,MAAM,mBAAmB,CACtD,CACF,CAAC;QACJ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,EAAE;QACA,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAC1B,KAAoB,EACpB,MAAc,EACd,MAA+D;IAE/D,IAAI,OAAO,GAAG,mBAAmB,MAAM,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACpE,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK;QAAE,OAAO,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IAC/D,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED;;;;GAIG;AACH,SAAS,YAAY,CACnB,KAAoB,EACpB,OAAe,EACf,eAAwB;IAExB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,KAAK,CAAC,eAAe,GAAG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,KAAK,CAAC,eAAe,CAAC;IACjE,OAAO,KAAK,CAAC;AACf,CAAC"}