{"version": 3, "file": "JSHandle.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/JSHandle.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAIH,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EACL,UAAU,EACV,sBAAsB,EACtB,oBAAoB,EACpB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EAClB,MAAM,gBAAgB,CAAC;AAExB,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAEzC;;GAEG;AACH,MAAM,WAAW,QAAQ;IACvB,OAAO,EAAE,KAAK,CAAC;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IACzC,OAAO,EAAE,KAAK,CAAC;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IACzC,MAAM,EAAE,KAAK,CAAC;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IACxC,MAAM,EAAE,KAAK,CAAC;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IACxC,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,wBAAgB,cAAc,CAC5B,OAAO,EAAE,gBAAgB,EACzB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,GAC1C,QAAQ,CAcV;AAQD;;;;;;;;;;;;;;;;;GAiBG;AACH,qBAAa,QAAQ,CAAC,gBAAgB,GAAG,OAAO;IAC9C;;OAEG;IACH,QAAQ,EAAE,gBAAgB,CAAC;IAC3B;;OAEG;IACH,OAAO,EAAE,UAAU,CAAC;IACpB;;OAEG;IACH,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC;IAC7C;;OAEG;IACH,SAAS,UAAS;IAElB;;OAEG;gBAED,OAAO,EAAE,gBAAgB,EACzB,MAAM,EAAE,UAAU,EAClB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY;IAO7C;OACG;IACH,gBAAgB,IAAI,gBAAgB;IAIpC;;;;;;;;;;OAUG;IAEG,QAAQ,CAAC,CAAC,SAAS,UAAU,CAAC,gBAAgB,CAAC,EACnD,YAAY,EAAE,CAAC,GAAG,MAAM,EACxB,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;IAMtD;;;;;;;;;;;;;;OAcG;IACG,cAAc,CAAC,UAAU,SAAS,QAAQ,GAAG,QAAQ,EACzD,YAAY,EAAE,gBAAgB,EAC9B,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,UAAU,CAAC;IAQtB;OACG;IACG,WAAW,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;IAgB1D;;;;;;;;;;;;;;;;OAgBG;IACG,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAarD;;;;;;;;OAQG;IACG,SAAS,CAAC,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC;IAa1C;;;OAGG;IACH,SAAS,IAAI,aAAa,GAAG,IAAI;IAOjC;;;OAGG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAM9B;;;;OAIG;IACH,QAAQ,IAAI,MAAM;CAOnB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,qBAAa,aAAa,CACxB,WAAW,SAAS,OAAO,GAAG,OAAO,CACrC,SAAQ,QAAQ,CAAC,WAAW,CAAC;IAC7B,OAAO,CAAC,MAAM,CAAQ;IACtB,OAAO,CAAC,KAAK,CAAO;IACpB,OAAO,CAAC,aAAa,CAAe;IAEpC;;OAEG;gBAED,OAAO,EAAE,gBAAgB,EACzB,MAAM,EAAE,UAAU,EAClB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,EAC3C,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,IAAI,EACV,YAAY,EAAE,YAAY;IAU5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACG,eAAe,CACnB,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,MAAM,CAAC,EAAE,OAAO,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;KACb,GACL,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAgBhC,SAAS,IAAI,aAAa,CAAC,WAAW,CAAC,GAAG,IAAI;IAI9C;;;OAGG;IACG,YAAY,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YAQ7B,uBAAuB;YA6CvB,gBAAgB;IA6B9B;;OAEG;IACG,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;IA6DrD,OAAO,CAAC,YAAY;IASpB,OAAO,CAAC,iBAAiB;IASzB,OAAO,CAAC,0BAA0B;IAWlC;;;;OAIG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAM5B;;;;OAIG;IACG,KAAK,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAMtD;;OAEG;IACG,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAU3D;;OAEG;IACG,SAAS,CACb,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA+C,GACnE,OAAO,CAAC,IAAI,CAAC;IAMhB;;OAEG;IACG,QAAQ,CACZ,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA+C,GACnE,OAAO,CAAC,IAAI,CAAC;IAMhB;;OAEG;IACG,IAAI,CACR,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA+C,GACnE,OAAO,CAAC,IAAI,CAAC;IAMhB;;OAEG;IACG,WAAW,CACf,MAAM,EAAE,aAAa,EACrB,OAAO,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAC1B,OAAO,CAAC,IAAI,CAAC;IAOhB;;;;;;;;;;;;;OAaG;IACG,MAAM,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAgCpD;;;;;;OAMG;IACG,UAAU,CAAC,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAgEvD;;;;OAIG;IACG,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC;IAM1B;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAM5B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAKpE;;;;;;;;;;;;;OAaG;IACG,KAAK,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;IAKjE;;;OAGG;IACG,WAAW,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAehD;;;;;;;OAOG;IACG,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAkC1C;;;;OAIG;IACG,UAAU,CAAC,OAAO,GAAE,iBAAsB,GAAG,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IAqD3E;;;OAGG;IACG,CAAC,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EACjC,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAMnC;;;OAGG;IACG,EAAE,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAClC,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAMnC;;;;;;;;;;;;;;OAcG;IACG,KAAK,CAAC,UAAU,EACpB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,CACZ,OAAO,EAAE,OAAO,EAChB,GAAG,IAAI,EAAE,OAAO,EAAE,KACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAyBzC;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACG,MAAM,CAAC,UAAU,EACrB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,CACZ,QAAQ,EAAE,OAAO,EAAE,EACnB,GAAG,IAAI,EAAE,OAAO,EAAE,KACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAiBzC;;;;OAIG;IACG,EAAE,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IA2BtD;;OAEG;IACG,sBAAsB,CAAC,OAAO,CAAC,EAAE;QACrC,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB,GAAG,OAAO,CAAC,OAAO,CAAC;CAarB;AAED;;GAEG;AACH,MAAM,WAAW,MAAM;IACrB;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,KAAK;IACpB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACX"}