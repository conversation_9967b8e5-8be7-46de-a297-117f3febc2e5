{"version": 3, "file": "NetworkManager.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/NetworkManager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,eAAe,EAAE,MAAM,6CAA6C,CAAC;AAC9E,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAG1C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,cAAc,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAE/E;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAEhC,QAAQ,EAAE,MAAM,CAAC;IAEjB,MAAM,EAAE,MAAM,CAAC;IAEf,OAAO,EAAE,MAAM,CAAC;CACjB;AACD;;GAEG;AACH,MAAM,WAAW,yBAA0B,SAAQ,iBAAiB;IAClE,OAAO,EAAE,OAAO,CAAC;CAClB;AAED;;;;;GAKG;AACH,eAAO,MAAM,2BAA2B;;;;;;CAM9B,CAAC;AAEX,UAAU,UAAW,SAAQ,YAAY;IACvC,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EAC3C,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;CACvD;AAED,UAAU,YAAY;IACpB,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;CACtC;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,YAAY;IAC9C,OAAO,EAAE,UAAU,CAAC;IACpB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,aAAa,EAAE,YAAY,CAAC;IAE5B,oBAAoB,sBAA6B;IAEjD,iBAAiB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAM;IAC/C,YAAY,CAAC,EAAE,WAAW,CAAQ;IAClC,yBAAyB,cAAqB;IAC9C,+BAA+B,UAAS;IACxC,mCAAmC,UAAS;IAC5C,kBAAkB,UAAS;IAC3B,0BAA0B,EAAE,yBAAyB,CAKnD;gBAGA,MAAM,EAAE,UAAU,EAClB,iBAAiB,EAAE,OAAO,EAC1B,YAAY,EAAE,YAAY;IAgCtB,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAQ3B,YAAY,CAAC,WAAW,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAKtD,mBAAmB,CACvB,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GACvC,OAAO,CAAC,IAAI,CAAC;IAehB,gBAAgB,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAI1C,qBAAqB,IAAI,MAAM;IAIzB,cAAc,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAK7C,wBAAwB,CAC5B,iBAAiB,EAAE,iBAAiB,GAAG,IAAI,GAC1C,OAAO,CAAC,IAAI,CAAC;IAcV,wBAAwB,IAAI,OAAO,CAAC,IAAI,CAAC;IASzC,YAAY,CAChB,SAAS,EAAE,MAAM,EACjB,iBAAiB,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GACvD,OAAO,CAAC,IAAI,CAAC;IAOV,eAAe,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAKhD,sBAAsB,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAKrD,kCAAkC,IAAI,OAAO,CAAC,IAAI,CAAC;IAoBzD,cAAc,IAAI,OAAO;IAInB,4BAA4B,IAAI,OAAO,CAAC,IAAI,CAAC;IAMnD,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,sBAAsB,GAAG,IAAI;IA2B1E,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI;IAwB9D;;;;;;;;OAQG;IACH,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI;IA0ChE,yBAAyB,CACvB,sBAAsB,EAAE,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAC/D,kBAAkB,EAAE,QAAQ,CAAC,KAAK,CAAC,kBAAkB,GACpD,IAAI;IAQP,UAAU,CACR,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAC9C,cAAc,CAAC,EAAE,cAAc,GAC9B,IAAI;IAoDP,yBAAyB,CACvB,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,2BAA2B,GAClD,IAAI;IAMP,sBAAsB,CACpB,OAAO,EAAE,WAAW,EACpB,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAC1C,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,8BAA8B,GACzD,IAAI;IAiBP,kBAAkB,CAChB,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,qBAAqB,EACxD,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,8BAA8B,GAAG,IAAI,GAChE,IAAI;IA6BP,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,qBAAqB,GAAG,IAAI;IAkBxE,4BAA4B,CAC1B,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,8BAA8B,GACrD,IAAI;IAkCP,cAAc,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,GAAG,IAAI;IAY3D,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI;IAatE,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI;IAaxE,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,kBAAkB,GAAG,IAAI;IAalE,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,kBAAkB,GAAG,IAAI;CAWrE"}