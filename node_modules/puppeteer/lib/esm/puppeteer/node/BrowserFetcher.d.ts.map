{"version": 3, "file": "BrowserFetcher.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/BrowserFetcher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAUH,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAyC/C;;;GAGG;AACH,oBAAY,QAAQ,GAAG,OAAO,GAAG,KAAK,GAAG,OAAO,GAAG,OAAO,CAAC;AAoE3D;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC;IACvB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,OAAO,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;CACjB;AACD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AAEH,qBAAa,cAAc;IACzB,OAAO,CAAC,QAAQ,CAAU;IAC1B,OAAO,CAAC,gBAAgB,CAAS;IACjC,OAAO,CAAC,aAAa,CAAS;IAC9B,OAAO,CAAC,SAAS,CAAW;IAE5B;;OAEG;gBACS,WAAW,EAAE,MAAM,EAAE,OAAO,GAAE,qBAA0B;IAkBpE,OAAO,CAAC,WAAW;IAcnB;;;OAGG;IACH,QAAQ,IAAI,QAAQ;IAIpB;;;OAGG;IACH,OAAO,IAAI,OAAO;IAIlB;;OAEG;IACH,IAAI,IAAI,MAAM;IAId;;;;;;;OAOG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAkB/C;;;;;;;;;OASG;IACG,QAAQ,CACZ,QAAQ,EAAE,MAAM,EAChB,gBAAgB,GAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,IAAqB,GAChE,OAAO,CAAC,0BAA0B,CAAC;IA+BtC;;;;;OAKG;IACG,cAAc,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IASzC;;;;;;OAMG;IACG,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAS7C;;;OAGG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,0BAA0B;IAkE1D;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;CAGzC"}