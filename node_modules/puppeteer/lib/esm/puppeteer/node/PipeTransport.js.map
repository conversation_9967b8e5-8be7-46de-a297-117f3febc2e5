{"version": 3, "file": "PipeTransport.js", "sourceRoot": "", "sources": ["../../../../src/node/PipeTransport.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EACL,MAAM,EACN,UAAU,GAEX,MAAM,qBAAqB,CAAC;AAG7B,MAAM,OAAO,aAAa;IAQxB,YACE,SAAgC,EAChC,QAA+B;QAE/B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG;YACrB,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,EAAE,CACnD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CACvB;YACD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBAC9C,IAAI,IAAI,CAAC,OAAO;oBAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC;YACtD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC;SACxD,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,SAAS,CAAC,MAAc;QACtB,IAAI,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,OAAO;SACR;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1E,IAAI,IAAI,CAAC,SAAS;YAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvD,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAClC,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE;YACjB,IAAI,IAAI,CAAC,SAAS;gBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;YACpE,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;YAChB,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACpD,CAAC;CACF"}