"use strict";
/**
 * Copyright 2020 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializePuppeteerWeb = void 0;
const Puppeteer_js_1 = require("./common/Puppeteer.js");
const initializePuppeteerWeb = (packageName) => {
    const isPuppeteerCore = packageName === 'puppeteer-core';
    return new Puppeteer_js_1.Puppeteer({
        isPuppeteerCore,
    });
};
exports.initializePuppeteerWeb = initializePuppeteerWeb;
//# sourceMappingURL=initialize-web.js.map