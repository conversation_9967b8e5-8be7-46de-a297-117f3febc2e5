{"version": 3, "file": "AriaQueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/AriaQueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,KAAK,UAAU,WAAW,CACxB,MAAkB,EAClB,OAAsB,EACtB,cAAuB,EACvB,IAAa;IAEb,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;QAC/D,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ;QACxC,cAAc;QACd,IAAI;KACL,CAAC,CAAC;IACH,MAAM,aAAa,GAAoC,KAAK,CAAC,MAAM,CACjE,CAAC,IAAmC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,CAC1E,CAAC;IACF,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE,CAC/C,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACnC,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AAClD,MAAM,eAAe,GACnB,yFAAyF,CAAC;AAa5F,SAAS,iBAAiB,CAAC,QAAgB;IACzC,MAAM,YAAY,GAAoB,EAAE,CAAC;IACzC,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAClC,eAAe,EACf,CAAC,CAAC,EAAE,SAAiB,EAAE,KAAa,EAAE,KAAa,EAAE,EAAE;QACrD,SAAS,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,SAAS,eAAe,CAAC,CAAC;QACvE,YAAY,CAAC,SAAS,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC,CACF,CAAC;IACF,IAAI,WAAW,IAAI,CAAC,YAAY,CAAC,IAAI;QACnC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;IAClD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,MAAM,QAAQ,GAAG,KAAK,EACpB,OAAsB,EACtB,QAAgB,EACe,EAAE;IACjC,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC1C,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACnD,MAAM,GAAG,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnE,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAO,IAAI,CAAC;KACb;IACD,OAAO,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,KAAK,EACnB,QAAkB,EAClB,QAAgB,EAChB,OAA+B,EACE,EAAE;IACnC,MAAM,OAAO,GAAgB;QAC3B,IAAI,EAAE,mBAAmB;QACzB,YAAY,EAAE,KAAK,EAAE,QAAgB,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/C,OAAO,OAAO,CAAC;QACjB,CAAC;KACF,CAAC;IACF,OAAO,QAAQ,CAAC,qBAAqB,CACnC,CAAC,CAAU,EAAE,QAAgB,EAAE,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EACxE,QAAQ,EACR,OAAO,EACP,OAAO,CACR,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,KAAK,EACpB,OAAsB,EACtB,QAAgB,EACU,EAAE;IAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC1C,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACnD,MAAM,GAAG,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnE,OAAO,OAAO,CAAC,GAAG,CAChB,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CACzE,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,KAAK,EACzB,OAAsB,EACtB,QAAgB,EACG,EAAE;IACrB,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACzD,MAAM,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CACpC,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,QAAQ,EACzB,GAAG,cAAc,CAClB,CAAC;IACF,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEF;;GAEG;AACU,QAAA,WAAW,GAAyB;IAC/C,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,aAAa;CACd,CAAC"}