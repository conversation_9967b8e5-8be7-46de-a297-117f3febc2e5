{"version": 3, "file": "QueryHandler.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAGxD;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC,QAAQ,CAAC,EAAE,CACT,OAAO,EAAE,aAAa,EACtB,QAAQ,EAAE,MAAM,KACb,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IACnC,OAAO,CAAC,EAAE,CACR,QAAQ,EAAE,QAAQ,EAClB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,sBAAsB,KAC5B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IACnC,QAAQ,CAAC,EAAE,CACT,OAAO,EAAE,aAAa,EACtB,QAAQ,EAAE,MAAM,KACb,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;IAC9B,aAAa,CAAC,EAAE,CACd,OAAO,EAAE,aAAa,EACtB,QAAQ,EAAE,MAAM,KACb,OAAO,CAAC,QAAQ,CAAC,CAAC;CACxB;AAED;;;;;;;;;;GAUG;AACH,MAAM,WAAW,kBAAkB;IACjC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE,QAAQ,EAAE,MAAM,KAAK,OAAO,GAAG,IAAI,CAAC;IAC7E,QAAQ,CAAC,EAAE,CACT,OAAO,EAAE,OAAO,GAAG,QAAQ,EAC3B,QAAQ,EAAE,MAAM,KACb,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;CACtC;AA8GD;;GAEG;AACH,wBAAgB,0BAA0B,CACxC,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,kBAAkB,GAC1B,IAAI,CAWN;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAI/D;AAED;;GAEG;AACH,wBAAgB,uBAAuB,IAAI,MAAM,EAAE,CAIlD;AAED;;GAEG;AACH,wBAAgB,wBAAwB,IAAI,IAAI,CAE/C;AAED;;GAEG;AACH,wBAAgB,0BAA0B,CAAC,QAAQ,EAAE,MAAM,GAAG;IAC5D,eAAe,EAAE,MAAM,CAAC;IACxB,YAAY,EAAE,oBAAoB,CAAC;CACpC,CAkBA"}