{"version": 3, "file": "Input.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/Input.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAiC,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAChF,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAMtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,qBAAa,QAAQ;IACnB,OAAO,CAAC,OAAO,CAAa;IAC5B,gBAAgB;IAChB,UAAU,SAAK;IACf,OAAO,CAAC,YAAY,CAAqB;IAEzC,gBAAgB;gBACJ,MAAM,EAAE,UAAU;IAI9B;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACG,IAAI,CACR,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE;QAAE,IAAI,CAAC,EAAE,MAAM,CAAA;KAAwB,GAC/C,OAAO,CAAC,IAAI,CAAC;IAsBhB,OAAO,CAAC,YAAY;IAQpB,OAAO,CAAC,wBAAwB;IAmChC;;;;;;OAMG;IACG,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAetC;;;;;;;;;;;;;;OAcG;IACG,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIhD,OAAO,CAAC,SAAS;IAIjB;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAYzE;;;;;;;;;;;;;;;;;;;OAmBG;IACG,KAAK,CACT,GAAG,EAAE,QAAQ,EACb,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAO,GAC9C,OAAO,CAAC,IAAI,CAAC;CAMjB;AAED;;GAEG;AACH,oBAAY,WAAW,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC;AAE3E;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwDG;AACH,qBAAa,KAAK;IAChB,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,SAAS,CAAW;IAC5B,OAAO,CAAC,EAAE,CAAK;IACf,OAAO,CAAC,EAAE,CAAK;IACf,OAAO,CAAC,OAAO,CAAgC;IAE/C;;OAEG;gBACS,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;IAKlD;;;;;;OAMG;IACG,IAAI,CACR,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAA;KAAO,GAC/B,OAAO,CAAC,IAAI,CAAC;IAiBhB;;;;;OAKG;IACG,KAAK,CACT,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,MAAM,EACT,OAAO,GAAE,YAAY,GAAG;QAAE,KAAK,CAAC,EAAE,MAAM,CAAA;KAAO,GAC9C,OAAO,CAAC,IAAI,CAAC;IAchB;;;OAGG;IACG,IAAI,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAarD;;;OAGG;IACG,EAAE,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAanD;;;;;;;;;;;;;;;;;;OAkBG;IACG,KAAK,CAAC,OAAO,GAAE,iBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;IAa3D;;;;OAIG;IACG,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAYzE;;;;OAIG;IACG,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAU5E;;;;OAIG;IACG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAU3E;;;;OAIG;IACG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAUvE;;;;;;;OAOG;IACG,WAAW,CACf,KAAK,EAAE,KAAK,EACZ,MAAM,EAAE,KAAK,EACb,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAA;KAAO,GAC/B,OAAO,CAAC,IAAI,CAAC;CAWjB;AAED;;;GAGG;AACH,qBAAa,WAAW;IACtB,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,SAAS,CAAW;IAE5B;;OAEG;gBACS,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;IAKlD;;;;OAIG;IACG,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAa/C"}