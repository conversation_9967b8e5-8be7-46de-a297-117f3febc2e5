{"version": 3, "file": "QueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/QueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,+DAAoD;AA4CpD,SAAS,gBAAgB,CAAC,OAA2B;IACnD,MAAM,eAAe,GAAyB,EAAE,CAAC;IAEjD,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC1E,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC3C,IAAI,aAAa;gBAAE,OAAO,aAAa,CAAC;YACxC,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QACF,eAAe,CAAC,OAAO,GAAG,CACxB,QAAkB,EAClB,QAAgB,EAChB,OAA+B,EAC/B,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;KAC1E;IAED,IAAI,OAAO,CAAC,QAAQ,EAAE;QACpB,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC1E,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,aAAa,EAAE,CAAC;YAClD,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE;gBAC1C,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC3C,IAAI,aAAa;oBAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/C;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QACF,eAAe,CAAC,aAAa,GAAG,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC1D,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,cAAc,CAC/C,OAAO,CAAC,QAAQ,EAChB,QAAQ,CACT,CAAC;YACF,MAAM,WAAW,GAAG,MAAM,YAAY,CAAC,cAAc,CACnD,CAAC,GAAoC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAC1D,CAAC;YACF,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC;KACH;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,MAAM,eAAe,GAAG,gBAAgB,CAAC;IACvC,QAAQ,EAAE,CAAC,OAAgB,EAAE,QAAgB,EAAE,EAAE,CAC/C,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;IACjC,QAAQ,EAAE,CAAC,OAAgB,EAAE,QAAgB,EAAE,EAAE,CAC/C,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC;CACrC,CAAC,CAAC;AAEH,MAAM,aAAa,GAAG,gBAAgB,CAAC;IACrC,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,IAAI,KAAK,GAAmB,IAAI,CAAC;QACjC,MAAM,MAAM,GAAG,CAAC,IAA0B,EAAE,EAAE;YAC5C,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;YACtE,GAAG;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA0B,CAAC;gBACpD,IAAI,WAAW,CAAC,UAAU,EAAE;oBAC1B,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;iBAChC;gBACD,IAAI,WAAW,YAAY,UAAU,EAAE;oBACrC,SAAS;iBACV;gBACD,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC3C,KAAK,GAAG,WAAW,CAAC;iBACrB;aACF,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;QACtC,CAAC,CAAC;QACF,IAAI,OAAO,YAAY,QAAQ,EAAE;YAC/B,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;SACnC;QACD,MAAM,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;QAC9B,MAAM,MAAM,GAAc,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,CAAC,IAA0B,EAAE,EAAE;YAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;YACtE,GAAG;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA0B,CAAC;gBACpD,IAAI,WAAW,CAAC,UAAU,EAAE;oBAC1B,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;iBACjC;gBACD,IAAI,WAAW,YAAY,UAAU,EAAE;oBACrC,SAAS;iBACV;gBACD,IAAI,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACjC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC1B;aACF,QAAQ,IAAI,CAAC,QAAQ,EAAE,EAAE;QAC5B,CAAC,CAAC;QACF,IAAI,OAAO,YAAY,QAAQ,EAAE;YAC/B,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;SACnC;QACD,OAAO,CAAC,OAAO,CAAC,CAAC;QACjB,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;IAC/B,CAAC,MAAM,EAAE,iCAAW,CAAC;IACrB,CAAC,QAAQ,EAAE,aAAa,CAAC;CAC1B,CAAC,CAAC;AACH,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAEjD;;GAEG;AACH,SAAgB,0BAA0B,CACxC,IAAY,EACZ,OAA2B;IAE3B,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,kBAAkB,CAAC,CAAC;IAE3E,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAE1E,MAAM,eAAe,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAElD,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AAC5C,CAAC;AAdD,gEAcC;AAED;;GAEG;AACH,SAAgB,4BAA4B,CAAC,IAAY;IACvD,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC3D,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC7B;AACH,CAAC;AAJD,oEAIC;AAED;;GAEG;AACH,SAAgB,uBAAuB;IACrC,OAAO,CAAC,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CACtC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CACtC,CAAC;AACJ,CAAC;AAJD,0DAIC;AAED;;GAEG;AACH,SAAgB,wBAAwB;IACtC,uBAAuB,EAAE,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;AAClE,CAAC;AAFD,4DAEC;AAED;;GAEG;AACH,SAAgB,0BAA0B,CAAC,QAAgB;IAIzD,MAAM,qBAAqB,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5D,IAAI,CAAC,qBAAqB;QACxB,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,EAAE,CAAC;IAEtE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACtC,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IAClD,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,KAAK,CACb,qBAAqB,IAAI,gDAAgD,CAC1E,CAAC;IAEJ,OAAO;QACL,eAAe;QACf,YAAY;KACb,CAAC;AACJ,CAAC;AArBD,gEAqBC"}