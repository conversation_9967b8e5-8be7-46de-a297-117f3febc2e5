{"version": 3, "file": "FrameManager.js", "sourceRoot": "", "sources": ["../../../../src/common/FrameManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,uDAAiD;AACjD,2CAAqC;AACrC,2CAAiD;AACjD,+DAAgF;AAChF,+DAG+B;AAC/B,+CAAiE;AACjE,2DAAqD;AAErD,mDAAyD;AAezD,MAAM,kBAAkB,GAAG,6BAA6B,CAAC;AACzD,MAAM,YAAY,GAAG,uBAAuB,CAAC;AAE7C;;;;;GAKG;AACU,QAAA,yBAAyB,GAAG;IACvC,aAAa,EAAE,MAAM,CAAC,4BAA4B,CAAC;IACnD,cAAc,EAAE,MAAM,CAAC,6BAA6B,CAAC;IACrD,aAAa,EAAE,MAAM,CAAC,4BAA4B,CAAC;IACnD,YAAY,EAAE,MAAM,CAAC,2BAA2B,CAAC;IACjD,cAAc,EAAE,MAAM,CAAC,6BAA6B,CAAC;IACrD,4BAA4B,EAAE,MAAM,CAClC,2CAA2C,CAC5C;IACD,uBAAuB,EAAE,MAAM,CAAC,sCAAsC,CAAC;IACvE,yBAAyB,EAAE,MAAM,CAAC,wCAAwC,CAAC;CAC5E,CAAC;AAEF;;GAEG;AACH,MAAa,YAAa,SAAQ,8BAAY;IAU5C,YACE,MAAkB,EAClB,IAAU,EACV,iBAA0B,EAC1B,eAAgC;QAEhC,KAAK,EAAE,CAAC;QAXF,YAAO,GAAG,IAAI,GAAG,EAAiB,CAAC;QACnC,wBAAmB,GAAG,IAAI,GAAG,EAA4B,CAAC;QAC1D,oBAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QAU1C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAc,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,mBAAmB,CAAC,OAAmB;QAC7C,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,KAAK,EAAE,EAAE;YACzC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,8BAA8B,EAAE,CAAC,KAAK,EAAE,EAAE;YACnD,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CACR,oBAAoB,EACpB,CAAC,KAAuC,EAAE,EAAE;YAC1C,IAAI,CAAC,gBAAgB,CACnB,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,MAAgD,CACvD,CAAC;QACJ,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,CAAC,KAAK,EAAE,EAAE;YAC/C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,iCAAiC,EAAE,CAAC,KAAK,EAAE,EAAE;YACtD,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,mCAAmC,EAAE,CAAC,KAAK,EAAE,EAAE;YACxD,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAClD,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACpD,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,2BAA2B,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAqB,IAAI,CAAC,OAAO;QAChD,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC;aACjC,CAAC,CAAC;YAEH,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAChE,MAAM;qBACH,IAAI,CAAC,gBAAgB,CAAC;qBACtB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;gBACpE,yDAAyD;gBACzD,MAAM,KAAK,IAAI,CAAC,OAAO;oBACrB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE;oBACnC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;aACtB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,wEAAwE;YACxE,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;gBACvC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EACxC;gBACA,OAAO;aACR;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,KAAY,EACZ,GAAW,EACX,UAII,EAAE;QAEN,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,EACJ,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,EAC5D,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QAEZ,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACtE,IAAI,2BAA2B,GAAG,KAAK,CAAC;QACxC,IAAI,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAC7B,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC;YAC/C,OAAO,CAAC,2BAA2B,EAAE;SACtC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBACzB,OAAO,CAAC,2BAA2B,EAAE;gBACrC,2BAA2B;oBACzB,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE;oBACxC,CAAC,CAAC,OAAO,CAAC,6BAA6B,EAAE;aAC5C,CAAC,CAAC;SACJ;QACD,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,KAAK;YAAE,MAAM,KAAK,CAAC;QACvB,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAE1C,KAAK,UAAU,QAAQ,CACrB,MAAkB,EAClB,GAAW,EACX,QAAgB,EAChB,OAAe;YAEf,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;oBAClD,GAAG;oBACH,QAAQ;oBACR,OAAO;iBACR,CAAC,CAAC;gBACH,2BAA2B,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAClD,OAAO,QAAQ,CAAC,SAAS;oBACvB,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,OAAO,GAAG,EAAE,CAAC;oBAC9C,CAAC,CAAC,IAAI,CAAC;aACV;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,KAAK,CAAC;aACd;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,KAAY,EACZ,UAGI,EAAE;QAEN,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACzC,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QACZ,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACtE,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YAC/B,OAAO,CAAC,2BAA2B,EAAE;YACrC,OAAO,CAAC,6BAA6B,EAAE;YACvC,OAAO,CAAC,4BAA4B,EAAE;SACvC,CAAC,CAAC;QACH,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,KAAK;YAAE,MAAM,KAAK,CAAC;QACvB,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,KAA4C;QAE5C,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE;YACtC,OAAO;SACR;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,OAAO,GAAG,0BAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAC1D,KAAK,CAAC,SAAS,CAChB,CAAC;QACF,IAAI,KAAK;YAAE,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,KAA8C;QAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;YAC/B,kDAAkD;YAClD,mDAAmD;YACnD,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;SACtC;IACH,CAAC;IAED,iBAAiB,CAAC,KAAwC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,SAAkC;QAElC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CACnB,OAAO,EACP,SAAS,CAAC,KAAK,CAAC,EAAE,EAClB,SAAS,CAAC,KAAK,CAAC,QAAQ,CACzB,CAAC;SACH;QACD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,WAAW;YAAE,OAAO;QAEnC,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE;YACzC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACvC;IACH,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAC3C,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,OAAe,EACf,aAAsB;QAEtB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;gBACjC,kDAAkD;gBAClD,iDAAiD;gBACjD,yBAAyB;gBACzB,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;aAC9B;YACD,OAAO;SACR;QACD,IAAA,kBAAM,EAAC,aAAa,CAAC,CAAC;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,iBAAiB,CAAC,YAAiC;QACjD,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC;QAC3C,IAAI,KAAK,GAAG,WAAW;YACrB,CAAC,CAAC,IAAI,CAAC,UAAU;YACjB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACtC,IAAA,kBAAM,EACJ,WAAW,IAAI,KAAK,EACpB,yEAAyE,CAC1E,CAAC;QAEF,iCAAiC;QACjC,IAAI,KAAK,EAAE;YACT,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE;gBACrC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;SACxC;QAED,+BAA+B;QAC/B,IAAI,WAAW,EAAE;YACf,IAAI,KAAK,EAAE;gBACT,wEAAwE;gBACxE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC/B,KAAK,CAAC,GAAG,GAAG,YAAY,CAAC,EAAE,CAAC;aAC7B;iBAAM;gBACL,iCAAiC;gBACjC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC9D;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SACzB;QAED,wBAAwB;QACxB,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAmB,EAAE,IAAY;QAC1D,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,OAAO;QAC1C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE9B,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE;YAC1D,MAAM,EAAE,iBAAiB,2CAAqB,EAAE;YAChD,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QACH,+CAA+C;QAC/C,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE;aACV,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC;aAC5C,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACb,OAAO;aACJ,IAAI,CAAC,0BAA0B,EAAE;YAChC,OAAO,EAAE,KAAK,CAAC,GAAG;YAClB,SAAS,EAAE,IAAI;YACf,mBAAmB,EAAE,IAAI;SAC1B,CAAC;aACD,KAAK,CAAC,sBAAU,CAAC,CACrB,CACJ,CAAC;IACJ,CAAC;IAED,+BAA+B,CAAC,OAAe,EAAE,GAAW;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB,CACd,OAAe,EACf,MAA8C;QAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,QAAQ,EAAE;YACvB,gEAAgE;YAChE,qCAAqC;YACrC,kEAAkE;YAClE,IAAI,KAAK;gBAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;SACjD;aAAM,IAAI,MAAM,KAAK,MAAM,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SAC1D;IACH,CAAC;IAED,0BAA0B,CACxB,cAA4D,EAC5D,OAAmB;QAEnB,MAAM,OAAO,GAAG,cAAc,CAAC,OAA+B,CAAC;QAC/D,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;QAChD,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,KAAK,EAAE;YACT,sEAAsE;YACtE,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO;gBAAE,OAAO;YAEtC,IAAI,cAAc,CAAC,OAAO,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACnE,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;aAC1B;iBAAM,IACL,cAAc,CAAC,IAAI,KAAK,kBAAkB;gBAC1C,CAAC,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,EACpC;gBACA,0EAA0E;gBAC1E,oEAAoE;gBACpE,qBAAqB;gBACrB,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;aAC/B;SACF;QACD,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,IAAI,CAAC,OAAO,EAC9B,cAAc,EACd,KAAK,CACN,CAAC;QACF,IAAI,KAAK;YAAE,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACtC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEO,4BAA4B,CAClC,kBAA0B,EAC1B,OAAmB;QAEnB,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,kBAAkB,EAAE,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,MAAM;YAAE,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEO,2BAA2B,CAAC,OAAmB;QACrD,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE;YAC/D,yDAAyD;YACzD,0BAA0B;YAC1B,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO;gBAAE,SAAS;YAC1C,IAAI,OAAO,CAAC,MAAM;gBAAE,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACtC;IACH,CAAC;IAED,oBAAoB,CAClB,SAAiB,EACjB,UAAsB,IAAI,CAAC,OAAO;QAElC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,SAAS,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAA,kBAAM,EAAC,OAAO,EAAE,4CAA4C,GAAG,SAAS,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,wBAAwB,CAAC,KAAY;QAC3C,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE;YACrC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACvC,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;CACF;AAnbD,oCAmbC;AAyED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,MAAa,KAAK;IA2ChB;;OAEG;IACH,YACE,YAA0B,EAC1B,WAAyB,EACzB,OAAe,EACf,MAAkB;QAvCZ,SAAI,GAAG,EAAE,CAAC;QACV,cAAS,GAAG,KAAK,CAAC;QAC1B;;WAEG;QACH,cAAS,GAAG,EAAE,CAAC;QAMf;;WAEG;QACH,qBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QA2BnC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEhE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAkB;QAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,sBAAQ,CAC5B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,IAAI,CAAC,aAAa,CAAC,gBAAgB,CACpC,CAAC;QACF,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAQ,CACjC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,IAAI,CAAC,aAAa,CAAC,gBAAgB,CACpC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACH,KAAK,CAAC,IAAI,CACR,GAAW,EACX,UAII,EAAE;QAEN,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,KAAK,CAAC,iBAAiB,CACrB,UAGI,EAAE;QAEN,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,KAAK,CAAC,cAAc,CAClB,YAA8B,EAC9B,GAAG,IAA8B;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAc,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,QAAQ,CACZ,YAAe,EACf,GAAG,IAA8B;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAI,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,CAAC,CACL,QAAgB;QAEhB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAI,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,UAAkB;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,KAAK,CACT,QAAgB,EAChB,YAGqC,EACrC,GAAG,IAA8B;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAa,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,MAAM,CACV,QAAgB,EAChB,YAGqC,EACrC,GAAG,IAA8B;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAa,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,EAAE,CACN,QAAgB;QAEhB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAI,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,UAAU,CACd,IAAY,EACZ,UAGI,EAAE;QAEN,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;;OASG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,GAAG;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,YAAY,CAChB,OAAiC;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,WAAW,CAAC,OAAgC;QAChD,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,KAAK,CAAC,KAAK,CACT,QAAgB,EAChB,UAII,EAAE;QAEN,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CAAC,QAAgB,EAAE,GAAG,MAAgB;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,GAAG,CAAC,QAAgB;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,IAAI,CACR,QAAgB,EAChB,IAAY,EACZ,OAA2B;QAE3B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,OAAO,CACL,2BAAuD,EACvD,UAAmC,EAAE,EACrC,GAAG,IAA8B;QAEjC,OAAO,CAAC,IAAI,CACV,iKAAiK,CAClK,CAAC;QAEF,IAAI,kBAAM,CAAC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;YAChD,MAAM,MAAM,GAAG,2BAA2B,CAAC;YAC3C,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;gBAAE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAC9C;QACD,IAAI,kBAAM,CAAC,QAAQ,CAAC,2BAA2B,CAAC;YAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAC7B,UAAU,CAAC,OAAO,EAAE,2BAA2B,CAAC,CACjD,CAAC;QACJ,IAAI,OAAO,2BAA2B,KAAK,UAAU;YACnD,OAAO,IAAI,CAAC,eAAe,CACzB,2BAA2B,EAC3B,OAAO,EACP,GAAG,IAAI,CACR,CAAC;QACJ,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,KAAK,CACP,2BAA2B,GAAG,OAAO,2BAA2B,CACjE,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,cAAc,CAAC,YAAoB;QACjC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,KAAK,CAAC,eAAe,CACnB,QAAgB,EAChB,UAAkC,EAAE;QAEpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CACvD,QAAQ,EACR,OAAO,CACR,CAAC;QACF,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QACtE,MAAM,MAAM,GAAG,MAAM,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACtE,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,YAAY,CAChB,KAAa,EACb,UAAkC,EAAE;QAEpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QACtE,MAAM,MAAM,GAAG,MAAM,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACtE,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,eAAe,CACb,YAA+B,EAC/B,UAAuC,EAAE,EACzC,GAAG,IAA8B;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,YAAiC;QAC1C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,GAAW;QAClC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB,EAAE,IAAY;QAC9C,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SAC/B;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,YAAY;YAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;CACF;AA7xBD,sBA6xBC;AAED,SAAS,+BAA+B,CAAC,OAExC;IACC,IAAA,kBAAM,EACJ,OAAO,CAAC,oBAAoB,CAAC,KAAK,SAAS,EAC3C,0DAA0D,CAC3D,CAAC;IACF,IAAA,kBAAM,EACJ,OAAO,CAAC,qBAAqB,CAAC,KAAK,SAAS,EAC5C,2DAA2D,CAC5D,CAAC;IACF,IAAA,kBAAM,EACJ,OAAO,CAAC,SAAS,KAAK,aAAa,EACnC,gFAAgF,CACjF,CAAC;AACJ,CAAC"}