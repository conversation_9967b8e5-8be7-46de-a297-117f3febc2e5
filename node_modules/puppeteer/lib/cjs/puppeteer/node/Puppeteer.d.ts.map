{"version": 3, "file": "Puppeteer.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/Puppeteer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,SAAS,EACT,uBAAuB,EACvB,cAAc,EACf,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,qBAAqB,CAAC;AAC5E,OAAO,EACL,aAAa,EACb,4BAA4B,EAC7B,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,qBAAqB,EAAE,MAAM,+BAA+B,CAAC;AACtE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAiB,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAE1D,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,OAAO,CAAC,aAAa,CAAC,CAAkB;IACxC,OAAO,CAAC,YAAY,CAAC,CAAS;IAC9B,OAAO,CAAC,aAAa,CAAC,CAAU;IAChC;;OAEG;IACH,kBAAkB,EAAE,MAAM,CAAC;IAE3B;;OAEG;gBAED,QAAQ,EAAE;QACR,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,WAAW,CAAC,EAAE,OAAO,CAAC;KACvB,GAAG,uBAAuB;IAU7B;;;;;;;OAOG;IACH,OAAO,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC;IAKlD;;OAEG;IACH,IAAI,YAAY,IAAI,OAAO,GAAG,SAAS,CAEtC;IAGD,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS,EAGzC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CACJ,OAAO,GAAE,aAAa,GACpB,4BAA4B,GAC5B,qBAAqB,GAAG;QACtB,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACxC,GACP,OAAO,CAAC,OAAO,CAAC;IAKnB;;;;;;;;;OASG;IACH,cAAc,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IAIxC;;OAEG;IACH,IAAI,SAAS,IAAI,eAAe,CAuB/B;IAED;;;;;;;OAOG;IACH,IAAI,OAAO,IAAI,MAAM,CAEpB;IAED;;;;OAIG;IACH,WAAW,CAAC,OAAO,GAAE,4BAAiC,GAAG,MAAM,EAAE;IAIjE;;;;OAIG;IACH,oBAAoB,CAAC,OAAO,EAAE,qBAAqB,GAAG,cAAc;CAQrE"}