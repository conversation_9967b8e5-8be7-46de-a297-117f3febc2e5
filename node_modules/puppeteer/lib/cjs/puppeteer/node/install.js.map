{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../../../src/node/install.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,4CAAoB;AACpB,kDAA8C;AAC9C,wDAAmC;AACnC,8CAAsB;AACtB,yDAAmC;AACnC,kDAAsD;AAEtD,0EAE2B;AAC3B,mDAAgD;AAEhD,MAAM,iBAAiB,GAAG;IACxB,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,iBAAiB;CAClB,CAAC;AAEX,SAAS,UAAU,CAAC,KAAa;IAC/B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;KACjD;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,KAAK,UAAU,eAAe;IACnC,MAAM,YAAY,GAChB,OAAO,CAAC,GAAG,CAAC,uBAAuB;QACnC,OAAO,CAAC,GAAG,CAAC,kCAAkC;QAC9C,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC;IACzD,MAAM,OAAO,GAAG,UAAU,CACxB,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAC3B,OAAO,CAAC,GAAG,CAAC,4BAA4B;QACxC,OAAO,CAAC,GAAG,CAAC,oCAAoC;QAChD,QAAQ,CACX,CAAC;IACF,MAAM,YAAY,GAChB,OAAO,CAAC,GAAG,CAAC,uBAAuB;QACnC,OAAO,CAAC,GAAG,CAAC,kCAAkC;QAC9C,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC;IACzD,MAAM,cAAc,GAAI,iBAA2B,CAAC,oBAAoB,CAAC;QACvE,OAAO;QACP,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,YAAY;KACnB,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,MAAM,WAAW,EAAE,CAAC;IACrC,MAAM,WAAW,CAAC,QAAQ,CAAC,CAAC;IAE5B,KAAK,UAAU,WAAW;QACxB,IAAI,OAAO,KAAK,QAAQ,EAAE;YACxB,OAAO,CACL,OAAO,CAAC,GAAG,CAAC,2BAA2B;gBACvC,OAAO,CAAC,GAAG,CAAC,sCAAsC;gBAClD,kCAAmB,CAAC,QAAQ,CAC7B,CAAC;SACH;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE;YAC/B,iBAA2B,CAAC,kBAAkB;gBAC7C,kCAAmB,CAAC,OAAO,CAAC;YAC9B,OAAO,wBAAwB,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAChD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;SACnD;IACH,CAAC;IAED,SAAS,WAAW,CAAC,QAAgB;QACnC,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE3D,oDAAoD;QACpD,IAAI,YAAY,CAAC,KAAK,EAAE;YACtB,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,YAAY,CAAC,UAAU,sBAAsB,CAC7F,CAAC;YACF,OAAO;SACR;QAED,8EAA8E;QAC9E,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACrE,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACpE,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAErD,IAAI,eAAe;YAAE,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,eAAe,CAAC;QAC/D,IAAI,cAAc;YAAE,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC;QAC5D,IAAI,YAAY;YAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,YAAY,CAAC;QAEtD,SAAS,SAAS,CAAC,cAAwB;YACzC,4DAA4D;YAC5D,iCAAiC;YACjC,IAAI,YAAE,CAAC,QAAQ,EAAE,KAAK,QAAQ,IAAI,YAAE,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;gBACvD,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,QAAQ,mBAAmB,YAAY,CAAC,UAAU,EAAE,CACpG,CAAC;aACH;YACD,cAAc,GAAG,cAAc,CAAC,MAAM,CACpC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,KAAK,YAAY,CAAC,QAAQ,CACjD,CAAC;YACF,MAAM,kBAAkB,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CACzD,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAChC,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,OAAO,CAAC,KAAY;YAC3B,OAAO,CAAC,KAAK,CACX,2BAA2B,iBAAiB,CAAC,OAAO,CAAC,KAAK,QAAQ,gEAAgE,CACnI,CAAC;YACF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,WAAW,GAAuB,IAAI,CAAC;QAC3C,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,SAAS,UAAU,CAAC,eAAuB,EAAE,UAAkB;YAC7D,IAAI,CAAC,WAAW,EAAE;gBAChB,WAAW,GAAG,IAAI,kBAAW,CAC3B,eACE,iBAAiB,CAAC,OAAO,CAC3B,KAAK,QAAQ,MAAM,WAAW,CAAC,UAAU,CAAC,yBAAyB,EACnE;oBACE,QAAQ,EAAE,GAAG;oBACb,UAAU,EAAE,GAAG;oBACf,KAAK,EAAE,EAAE;oBACT,KAAK,EAAE,UAAU;iBAClB,CACF,CAAC;aACH;YACD,MAAM,KAAK,GAAG,eAAe,GAAG,mBAAmB,CAAC;YACpD,mBAAmB,GAAG,eAAe,CAAC;YACtC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,cAAc;aAClB,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC;aAC3C,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;aAC3C,IAAI,CAAC,SAAS,CAAC;aACf,KAAK,CAAC,OAAO,CAAC,CAAC;IACpB,CAAC;IAED,SAAS,WAAW,CAAC,KAAa;QAChC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAC/B,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;IAC1C,CAAC;IAED,KAAK,UAAU,wBAAwB;QACrC,MAAM,kBAAkB,GACtB,+DAA+D,CAAC;QAElE,MAAM,QAAQ,GAAG,IAAA,+BAAc,EAAC,kBAAkB,CAAC,CAAC;QAEpD,MAAM,cAAc,GAAmB,EAAE,CAAC;QAE1C,IAAI,QAAQ,EAAE;YACZ,MAAM,cAAc,GAAG,aAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAE3C,MAAM,YAAY,GAAG;gBACnB,GAAG,cAAc;gBACjB,WAAW,EAAE,cAAc,CAAC,QAAQ,KAAK,QAAQ;aACxB,CAAC;YAE5B,cAAc,CAAC,KAAK,GAAG,IAAA,2BAAqB,EAAC,YAAY,CAAC,CAAC;YAC3D,cAAc,CAAC,kBAAkB,GAAG,KAAK,CAAC;SAC3C;QAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtD,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,WAAW,CACT,kDAAkD,kBAAkB,EAAE,CACvE,CAAC;YACF,eAAK;iBACF,GAAG,CAAC,kBAAkB,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,EAAE;gBAC7C,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,IAAI,GAAG;oBACrC,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;oBACrB,IAAI,IAAI,KAAK,CAAC;gBAChB,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACf,IAAI;wBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAClC,OAAO,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;qBAC1C;oBAAC,MAAM;wBACN,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;qBACvD;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAvKD,0CAuKC;AAED,SAAgB,WAAW,CAAC,UAAmB;IAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE,CAAC;IACvD,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3E,sCAAsC;IACtC,IAAI,CAAC,eAAe;QAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAChD,CAAC;AAND,kCAMC"}