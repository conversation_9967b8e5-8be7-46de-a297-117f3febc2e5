{"name": "chromedriver", "version": "110.0.0", "keywords": ["chromedriver", "selenium"], "description": "ChromeDriver for Selenium", "homepage": "https://github.com/giggio/node-chromedriver", "repository": {"type": "git", "url": "git://github.com/giggio/node-chromedriver.git"}, "license": "Apache-2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.lambda3.com.br"}, "main": "lib/chromedriver", "bin": {"chromedriver": "bin/chromedriver"}, "scripts": {"install": "node install.js", "update-chromedriver": "node update.js", "typecheck": "tsc", "lint": "eslint"}, "dependencies": {"@testim/chrome-version": "^1.1.3", "axios": "^1.2.1", "compare-versions": "^5.0.1", "extract-zip": "^2.0.1", "https-proxy-agent": "^5.0.1", "proxy-from-env": "^1.1.0", "tcp-port-used": "^1.0.1"}, "devDependencies": {"eslint": "^8.29.0", "typescript": "^4.9.3"}, "engines": {"node": ">=14"}}