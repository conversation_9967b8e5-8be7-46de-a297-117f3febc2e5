

main()

g_id_pl = '57f5a0fc7e7a43729fedbf685aebcdfe'

async function main() {
    try {
        const browser = await openBrowerById(g_id_pl)
        const pages = await browser.pages()
        console.log('pages length ===>>> ', pages.length)
        await sleep(5000)
    } catch (err) {
        console.error(err)
    }
}

async function openBrowerById(nID) {
    try{
        const res = await openBrowser({
        id:id,
        args: [],
        loadExtensions: false,
        extractIp: false
        })

        // 判断是否打开成功，打开成功则连接浏览器
        if (res.success) {
            const browser = await puppeteer.connect({
                browserWSEndpoint: res.data.ws,
                defaultViewport: null
            })
            return browser
        } else {
            console.error('浏览器打开失败')
        }
    } catch (err) {
        console.error(err)
    }
}

function sleep(timeout) {
  return new Promise(resolve => {
    setTimeout(resolve, timeout)
  })
}