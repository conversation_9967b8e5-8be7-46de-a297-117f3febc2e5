/**
 * @description 连接到已打开的比特浏览器窗口的简单示例
 * @description 使用 Playwright 连接，不重新打开窗口
 */

const { chromium } = require('playwright')
const { getLocalOpenBrowsersDetail, getBrowserWebSocketEndpoint } = require('./request')

/**
 * @description 连接到第一个本地打开的比特浏览器窗口
 * @returns {Promise<Object>} 返回 { browser, context, page } 或错误信息
 */
async function connectToFirstLocalBrowser() {
  try {
    console.log('🔍 查找本地打开的浏览器窗口...')
    
    // 1. 获取本地打开的窗口
    const localOpenResult = await getLocalOpenBrowsersDetail()
    
    if (!localOpenResult.success || localOpenResult.count === 0) {
      throw new Error('没有找到本地打开的浏览器窗口')
    }
    
    const targetBrowser = localOpenResult.localOpenBrowsers[0]
    console.log(`🎯 连接到窗口: ${targetBrowser.name} (PID: ${targetBrowser.pid})`)
    
    // 2. 获取WebSocket端点
    const wsResult = await getBrowserWebSocketEndpoint(targetBrowser.id)
    
    if (!wsResult.success) {
      throw new Error(`无法获取WebSocket端点: ${wsResult.error}`)
    }
    
    console.log(`🔗 WebSocket端点: ${wsResult.wsEndpoint}`)
    
    // 3. 连接到浏览器
    const browser = await chromium.connectOverCDP(wsResult.wsEndpoint)
    console.log(`✅ 成功连接到浏览器`)
    
    // 4. 获取或创建上下文和页面
    let context, page
    const contexts = browser.contexts()
    
    if (contexts.length > 0) {
      context = contexts[0]
      const pages = context.pages()
      if (pages.length > 0) {
        page = pages[0]
      } else {
        page = await context.newPage()
      }
    } else {
      context = await browser.newContext()
      page = await context.newPage()
    }
    
    console.log(`📄 当前页面: ${await page.title()}`)
    console.log(`🌐 当前URL: ${page.url()}`)
    
    return {
      success: true,
      browser,
      context,
      page,
      browserInfo: targetBrowser
    }
    
  } catch (error) {
    console.error('❌ 连接失败:', error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * @description 连接到指定ID的比特浏览器窗口
 * @param {String} browserId 窗口ID
 * @returns {Promise<Object>} 返回连接结果
 */
async function connectToBrowserById(browserId) {
  try {
    console.log(`🔍 查找窗口 ID: ${browserId}`)
    
    // 1. 获取本地打开的窗口
    const localOpenResult = await getLocalOpenBrowsersDetail()
    
    if (!localOpenResult.success) {
      throw new Error('无法获取本地打开窗口信息')
    }
    
    const targetBrowser = localOpenResult.localOpenBrowsers.find(b => b.id === browserId)
    if (!targetBrowser) {
      throw new Error(`窗口 ${browserId} 未在本地打开`)
    }
    
    console.log(`🎯 连接到窗口: ${targetBrowser.name} (PID: ${targetBrowser.pid})`)
    
    // 2. 获取WebSocket端点
    const wsResult = await getBrowserWebSocketEndpoint(browserId)
    
    if (!wsResult.success) {
      throw new Error(`无法获取WebSocket端点: ${wsResult.error}`)
    }
    
    console.log(`🔗 WebSocket端点: ${wsResult.wsEndpoint}`)
    
    // 3. 连接到浏览器
    const browser = await chromium.connectOverCDP(wsResult.wsEndpoint)
    console.log(`✅ 成功连接到浏览器`)
    
    // 4. 获取或创建上下文和页面
    let context, page
    const contexts = browser.contexts()
    
    if (contexts.length > 0) {
      context = contexts[0]
      const pages = context.pages()
      if (pages.length > 0) {
        page = pages[0]
      } else {
        page = await context.newPage()
      }
    } else {
      context = await browser.newContext()
      page = await context.newPage()
    }
    
    return {
      success: true,
      browser,
      context,
      page,
      browserInfo: targetBrowser
    }
    
  } catch (error) {
    console.error('❌ 连接失败:', error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * @description 使用示例
 */
async function example() {
  console.log('=== 比特浏览器 Playwright 连接示例 ===\n')
  
  // 连接到第一个本地打开的窗口
  const result = await connectToFirstLocalBrowser()
  
  if (!result.success) {
    console.error('连接失败，请确保有比特浏览器窗口在本地打开')
    return
  }
  
  const { browser, context, page } = result
  
  try {
    // 示例操作1: 获取页面信息
    console.log('\n📊 页面信息:')
    console.log(`  标题: ${await page.title()}`)
    console.log(`  URL: ${page.url()}`)
    
    // 示例操作2: 执行JavaScript
    console.log('\n🔧 执行JavaScript:')
    const userAgent = await page.evaluate(() => navigator.userAgent)
    console.log(`  User Agent: ${userAgent}`)
    
    // 示例操作3: 获取页面元素（如果页面有内容）
    try {
      const bodyText = await page.evaluate(() => document.body?.innerText?.substring(0, 100))
      if (bodyText) {
        console.log(`  页面内容预览: ${bodyText}...`)
      }
    } catch (e) {
      console.log('  无法获取页面内容')
    }
    
    // 示例操作4: 导航到新页面（取消注释来测试）
    // console.log('\n🚀 导航到新页面...')
    // await page.goto('https://www.example.com')
    // console.log(`  新页面标题: ${await page.title()}`)
    
    console.log('\n✅ 示例操作完成')
    
  } catch (error) {
    console.error('❌ 执行操作时出错:', error.message)
  }
  
  // 注意：不要关闭browser，这会关闭比特浏览器窗口
  console.log('\n💡 提示: 浏览器窗口保持打开状态')
}

// 运行示例
if (require.main === module) {
  example().catch(console.error)
}

module.exports = {
  connectToFirstLocalBrowser,
  connectToBrowserById
}
