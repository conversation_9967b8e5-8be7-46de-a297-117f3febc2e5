const fs = require('fs');
const path = require('path');

const FILE_PATH = path.join(__dirname, '银行卡.txt');

function readAllCards() {
  if (!fs.existsSync(FILE_PATH)) {
    throw new Error('银行卡.txt 文件不存在');
  }
  const lines = fs.readFileSync(FILE_PATH, 'utf-8')
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);

  return lines.map(line => {
    const parts = line.split(/\s+/);
    const statusList = parts.slice(3);
    return {
      cardNumber: parts[0] || '',
      expire: parts[1] || '',
      cvv: parts[2] || '',
      statusList: statusList.length ? statusList : [],
    };
  });
}

function writeAllCards(cards) {
  const lines = cards.map(c => {
    const statusStr = c.statusList.join(' ');
    return `${c.cardNumber} ${c.expire} ${c.cvv} ${statusStr}`;
  });
  fs.writeFileSync(FILE_PATH, lines.join('\n'), 'utf-8');
}

/**
 * 获取一张“失败最少 && 成功最少 && 未被占用”的卡
 * 选定后立即打上“使用中”状态
 */
function getNextAvailableCard() {
  const cards = readAllCards();

  // 所有不是失败并且不含“使用中”的卡
  const candidates = cards
    .map((card, index) => {
      const failCount = card.statusList.filter(s => s === '失败').length;
      const successCount = card.statusList.filter(s => s === '成功').length;
      const isUsing = card.statusList.includes('使用中');
      return { index, card, failCount, successCount, isUsing };
    })
    .filter(c => c.failCount === 0 && !c.isUsing);

  if (candidates.length === 0) {
    throw new Error('所有卡都失败或正在使用，无法获取新卡');
  }

  // 按成功次数升序排序
  candidates.sort((a, b) => a.successCount - b.successCount);

  const selected = candidates[0];
  selected.card.statusList.push('使用中');

  // 更新文件写入
  cards[selected.index] = selected.card;
  writeAllCards(cards);

  return {
    index: selected.index,
    ...selected.card,
  };
}

/**
 * 更新卡的最终状态（将“使用中”替换为“成功”或“失败”）
 * @param {number} index 
 * @param {'成功'|'失败'} result 
 */
function markCardResult(index, result) {
  const cards = readAllCards();
  if (index < 0 || index >= cards.length) {
    throw new Error(`无效的卡索引：${index}`);
  }

  const statusList = cards[index].statusList;
  const usingIndex = statusList.lastIndexOf('使用中');

  if (usingIndex !== -1) {
    statusList[usingIndex] = result;
  } else {
    // 如果找不到“使用中”，则直接追加
    statusList.push(result);
  }

  cards[index].statusList = statusList;
  writeAllCards(cards);
}

module.exports = {
  getNextAvailableCard,
  markCardResult
};
