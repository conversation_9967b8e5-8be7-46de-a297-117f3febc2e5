const fs = require('fs');
const path = require('path');

const FILE_PATH = path.join(__dirname, '银行卡.txt');

function readAllCards() {
  if (!fs.existsSync(FILE_PATH)) {
    throw new Error('银行卡.txt 文件不存在');
  }
  const lines = fs.readFileSync(FILE_PATH, 'utf-8')
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);

  return lines.map(line => {
    const parts = line.split(/\s+/);
    const statusList = parts.slice(3);
    return {
      cardNumber: parts[0] || '',
      expire: parts[1] || '',
      cvv: parts[2] || '',
      statusList: statusList.length ? statusList : [],
    };
  });
}

function writeAllCards(cards) {
  const lines = cards.map(c => {
    const statusStr = c.statusList.join(' ');
    return `${c.cardNumber} ${c.expire} ${c.cvv} ${statusStr}`;
  });
  fs.writeFileSync(FILE_PATH, lines.join('\n'), 'utf-8');
}

/**
 * 获取一张“失败最少 && 成功最少 && 使用中最少”的卡
 * 选定后立即追加“使用中”状态
 * @param {boolean} allowUsing 是否允许分配正在“使用中”的卡（默认 false）
 */
function getNextAvailableCard(allowUsing = false) {
  const cards = readAllCards();

  const allValidCards = cards
    .map((card, index) => {
      const failCount = card.statusList.filter(s => s === '失败').length;
      const successCount = card.statusList.filter(s => s === '成功').length;
      const usingCount = card.statusList.filter(s => s === '使用中').length;
      return { index, card, failCount, successCount, usingCount };
    })
    .filter(c => c.failCount === 0);

  if (allValidCards.length === 0) {
    throw new Error('所有卡都失败，无法继续使用');
  }

  let candidates;

  if (allowUsing) {
    candidates = allValidCards;
  } else {
    const idleCards = allValidCards.filter(c => c.usingCount === 0);
    if (idleCards.length === 0) {
      throw new Error('所有卡都在使用中，且配置为不允许重复使用');
    }
    candidates = idleCards;
  }

  candidates.sort((a, b) => {
    if (a.successCount !== b.successCount) return a.successCount - b.successCount;
    return a.usingCount - b.usingCount;
  });

  const selected = candidates[0];
  selected.card.statusList.push('使用中');

  cards[selected.index] = selected.card;
  writeAllCards(cards);

  return {
    index: selected.index,
    ...selected.card,
  };
}

/**
 * 将“最近一个使用中”替换为 成功 / 失败
 * @param {number} index 
 * @param {'成功' | '失败'} result 
 */
function markCardResult(index, result) {
  const cards = readAllCards();
  if (index < 0 || index >= cards.length) {
    throw new Error(`无效的卡索引：${index}`);
  }

  const statusList = cards[index].statusList;
  const lastUsingIndex = statusList.lastIndexOf('使用中');

  if (lastUsingIndex !== -1) {
    statusList[lastUsingIndex] = result;
  } else {
    statusList.push(result); // 如果意外找不到“使用中”
  }

  cards[index].statusList = statusList;
  writeAllCards(cards);
}

module.exports = {
  getNextAvailableCard,
  markCardResult
};
