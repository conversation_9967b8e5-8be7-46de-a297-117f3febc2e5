# 比特浏览器 Playwright 连接指南

## 🎯 功能说明

这个解决方案可以让你用 Playwright 连接到**已经打开**的比特浏览器窗口，而不需要重新打开窗口。

## 🔧 核心功能

1. **识别本地打开的窗口** - 区分哪些窗口真正在本地运行
2. **获取WebSocket端点** - 自动获取连接信息
3. **Playwright连接** - 直接连接到已打开的窗口
4. **保持窗口状态** - 不会关闭或重启浏览器窗口

## 📁 文件说明

- `request.js` - 比特浏览器API封装，包含连接功能
- `connectToBitBrowser.js` - 简单的连接示例
- `playwrightConnectExample.js` - 详细的连接示例
- `localOpenBrowsersExample.js` - 窗口识别示例

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install playwright
```

### 2. 基本使用

```javascript
const { connectToFirstLocalBrowser } = require('./connectToBitBrowser')

async function main() {
  // 连接到第一个本地打开的窗口
  const result = await connectToFirstLocalBrowser()
  
  if (result.success) {
    const { browser, context, page } = result
    
    // 现在你可以使用 Playwright 操作浏览器了
    console.log('当前页面标题:', await page.title())
    console.log('当前URL:', page.url())
    
    // 执行其他操作...
    await page.goto('https://www.example.com')
    await page.screenshot({ path: 'screenshot.png' })
    
    // 注意：不要调用 browser.close()，这会关闭比特浏览器窗口
  } else {
    console.error('连接失败:', result.error)
  }
}

main()
```

### 3. 连接到指定窗口

```javascript
const { connectToBrowserById } = require('./connectToBitBrowser')

async function connectToSpecificBrowser() {
  const browserId = 'your-browser-id-here'
  const result = await connectToBrowserById(browserId)
  
  if (result.success) {
    const { page } = result
    // 操作页面...
  }
}
```

## 📊 工作原理

### 1. 识别本地窗口

```javascript
const { getLocalOpenBrowsersDetail } = require('./request')

const localBrowsers = await getLocalOpenBrowsersDetail()
// 返回真正在本地运行的窗口（有PID的窗口）
```

### 2. 获取WebSocket端点

```javascript
const { getBrowserWebSocketEndpoint } = require('./request')

const wsResult = await getBrowserWebSocketEndpoint(browserId)
if (wsResult.success) {
  console.log('WebSocket端点:', wsResult.wsEndpoint)
}
```

### 3. Playwright连接

```javascript
const { chromium } = require('playwright')

const browser = await chromium.connectOverCDP(wsEndpoint)
// 现在可以使用所有 Playwright 功能
```

## 🔍 测试结果

运行测试显示成功连接到比特浏览器：

```
🔍 查找本地打开的浏览器窗口...
🎯 连接到窗口: 跑料 - 验证支付 (PID: 13597)
🔗 WebSocket端点: ws://127.0.0.1:61740/devtools/browser/...
✅ 成功连接到浏览器
📄 当前页面: 6302-跑料 - 验证支付-工作台
```

## 💡 重要提示

### ✅ 可以做的操作

- 页面导航 (`page.goto()`)
- 元素操作 (`page.click()`, `page.fill()` 等)
- JavaScript执行 (`page.evaluate()`)
- 截图 (`page.screenshot()`)
- 等待元素 (`page.waitForSelector()`)
- 获取页面内容 (`page.content()`)

### ❌ 不要做的操作

- **不要调用 `browser.close()`** - 这会关闭比特浏览器窗口
- **不要调用 `context.close()`** - 这可能影响窗口状态
- 避免修改浏览器级别的设置

### 🔧 连接方式

系统会自动尝试两种方式获取WebSocket端点：

1. **端口扫描** - 扫描常见的调试端口 (9222-9235)
2. **API获取** - 通过比特浏览器API获取连接信息

## 📝 示例代码

### 完整示例

```javascript
const { connectToFirstLocalBrowser } = require('./connectToBitBrowser')

async function automateTask() {
  const result = await connectToFirstLocalBrowser()
  
  if (!result.success) {
    console.error('连接失败:', result.error)
    return
  }
  
  const { page } = result
  
  try {
    // 1. 获取当前页面信息
    console.log('当前页面:', await page.title())
    
    // 2. 导航到新页面
    await page.goto('https://www.facebook.com')
    await page.waitForLoadState('domcontentloaded')
    
    // 3. 查找并操作元素
    const loginButton = await page.locator('[data-testid="open-registration-form-button"]')
    if (await loginButton.isVisible()) {
      console.log('找到登录按钮')
    }
    
    // 4. 执行JavaScript
    const pageInfo = await page.evaluate(() => ({
      title: document.title,
      url: location.href,
      userAgent: navigator.userAgent
    }))
    console.log('页面信息:', pageInfo)
    
    // 5. 截图
    await page.screenshot({ path: 'current_page.png' })
    console.log('截图已保存')
    
  } catch (error) {
    console.error('操作失败:', error.message)
  }
  
  // 注意：不调用 browser.close()
  console.log('任务完成，浏览器窗口保持打开')
}

automateTask()
```

## 🛠️ 故障排除

### 问题1: 找不到本地打开的窗口
- 确保比特浏览器窗口确实在本地打开
- 运行 `node localOpenBrowsersExample.js` 检查窗口状态

### 问题2: 无法获取WebSocket端点
- 检查比特浏览器是否启用了调试模式
- 确保没有防火墙阻止本地端口访问

### 问题3: Playwright连接失败
- 确保安装了正确版本的 Playwright
- 检查WebSocket端点是否可访问

## 📞 支持

如果遇到问题，可以：
1. 检查比特浏览器是否正常运行
2. 确认窗口确实在本地打开（有PID）
3. 查看控制台错误信息
4. 尝试重新启动比特浏览器窗口
