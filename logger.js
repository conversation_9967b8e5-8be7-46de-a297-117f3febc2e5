/**
 * @description 日志模块 - 为BitBrowser自动化提供带窗口序号的日志功能
 * <AUTHOR> Automation Team
 */

/**
 * @description 创建带窗口 seq 编号的日志器
 * @param {number|string} seq 窗口序号
 * @returns {object} 日志器对象，包含 info, warn, error, log, debug 方法
 */
function createLogger(seq) {
    const prefix = `【${seq}】`
    
    /**
     * 内部日志函数
     * @param {string} level 日志级别
     * @param {string} message 日志消息
     * @param {...any} args 其他参数
     */
    function logger(level, message, ...args) {
        const fullMessage = `${prefix}: ${message}`
        
        switch (level) {
            case 'info':
                console.info(fullMessage, ...args)
                break
            case 'warn':
                console.warn(fullMessage, ...args)
                break
            case 'error':
                console.error(fullMessage, ...args)
                break
            case 'log':
            default:
                console.log(fullMessage, ...args)
                break
        }
    }
    
    return {
        /**
         * 信息日志 - 用于重要操作和状态变化
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        info: (message, ...args) => logger('info', message, ...args),
        
        /**
         * 警告日志 - 用于警告信息和异常情况
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        warn: (message, ...args) => logger('warn', message, ...args),
        
        /**
         * 错误日志 - 用于错误信息和失败操作
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        error: (message, ...args) => logger('error', message, ...args),
        
        /**
         * 普通日志 - 用于一般信息输出
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        log: (message, ...args) => logger('log', message, ...args),
        
        /**
         * 调试日志 - 用于调试信息和详细跟踪
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        debug: (message, ...args) => logger('log', `[DEBUG] ${message}`, ...args)
    }
}

/**
 * 创建全局日志器（不带窗口序号）
 * @returns {object} 全局日志器对象
 */
function createGlobalLogger() {
    return {
        info: (message, ...args) => console.info(`[GLOBAL]: ${message}`, ...args),
        warn: (message, ...args) => console.warn(`[GLOBAL]: ${message}`, ...args),
        error: (message, ...args) => console.error(`[GLOBAL]: ${message}`, ...args),
        log: (message, ...args) => console.log(`[GLOBAL]: ${message}`, ...args),
        debug: (message, ...args) => console.log(`[GLOBAL]: [DEBUG] ${message}`, ...args)
    }
}

module.exports = {
    createLogger,
    createGlobalLogger
}
