/**
 * @description 使用 Playwright 连接到已打开的比特浏览器窗口
 * @description 不重新打开窗口，直接连接到本地已打开的窗口
 */

const { chromium } = require('playwright')
const { getLocalOpenBrowsersDetail, getBrowserWebSocketEndpoint } = require('./request')

/**
 * @description 连接到本地打开的比特浏览器窗口
 * @param {String} browserId 可选的窗口ID，如果不提供则连接第一个找到的窗口
 * @returns {Promise<Object>} 返回连接结果
 */
async function connectToLocalBrowser(browserId = null) {
  console.log('🔍 正在查找本地打开的浏览器窗口...')
  
  try {
    // 1. 获取本地打开的窗口
    const localOpenResult = await getLocalOpenBrowsersDetail()
    
    if (!localOpenResult.success || localOpenResult.count === 0) {
      return {
        success: false,
        error: '没有找到本地打开的浏览器窗口'
      }
    }
    
    // 2. 选择要连接的窗口
    let targetBrowser
    if (browserId) {
      targetBrowser = localOpenResult.localOpenBrowsers.find(b => b.id === browserId)
      if (!targetBrowser) {
        return {
          success: false,
          error: `指定的窗口 ${browserId} 未在本地打开`
        }
      }
    } else {
      // 如果没有指定窗口ID，连接第一个找到的窗口
      targetBrowser = localOpenResult.localOpenBrowsers[0]
    }
    
    console.log(`🎯 准备连接到窗口: ${targetBrowser.name} (ID: ${targetBrowser.id}, PID: ${targetBrowser.pid})`)
    
    // 3. 获取WebSocket端点
    const wsResult = await getBrowserWebSocketEndpoint(targetBrowser.id)
    
    if (!wsResult.success) {
      return {
        success: false,
        error: `无法获取窗口的WebSocket端点: ${wsResult.error}`
      }
    }
    
    console.log(`🔗 找到WebSocket端点: ${wsResult.wsEndpoint}`)
    
    // 4. 使用Playwright连接
    const browser = await chromium.connectOverCDP(wsResult.wsEndpoint)
    
    console.log(`✅ 成功连接到浏览器窗口`)
    
    return {
      success: true,
      browser,
      browserInfo: targetBrowser,
      wsEndpoint: wsResult.wsEndpoint,
      port: wsResult.port
    }
    
  } catch (error) {
    console.error('❌ 连接浏览器时出错:', error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * @description 连接到指定窗口并执行操作
 * @param {String} browserId 窗口ID
 * @param {Function} callback 要执行的操作函数
 */
async function connectAndExecute(browserId, callback) {
  console.log(`\n=== 连接并执行操作 ===`)
  
  const connectResult = await connectToLocalBrowser(browserId)
  
  if (!connectResult.success) {
    console.error('连接失败:', connectResult.error)
    return
  }
  
  try {
    const { browser } = connectResult
    
    // 获取所有上下文
    const contexts = browser.contexts()
    console.log(`📄 找到 ${contexts.length} 个浏览器上下文`)
    
    if (contexts.length === 0) {
      console.log('⚠️  没有找到浏览器上下文，创建新的上下文')
      const context = await browser.newContext()
      const page = await context.newPage()
      await callback(browser, context, page)
    } else {
      // 使用第一个上下文
      const context = contexts[0]
      const pages = context.pages()
      console.log(`📑 找到 ${pages.length} 个页面`)
      
      let page
      if (pages.length === 0) {
        console.log('创建新页面...')
        page = await context.newPage()
      } else {
        page = pages[0]
      }
      
      await callback(browser, context, page)
    }
    
  } catch (error) {
    console.error('❌ 执行操作时出错:', error.message)
  } finally {
    // 注意：不要关闭browser，因为这会关闭比特浏览器窗口
    console.log('✅ 操作完成，保持浏览器窗口打开')
  }
}

/**
 * @description 示例：连接到浏览器并获取页面信息
 */
async function exampleGetPageInfo() {
  console.log('\n=== 示例：获取页面信息 ===')
  
  await connectAndExecute(null, async (browser, context, page) => {
    console.log('📊 页面信息:')
    console.log(`  标题: ${await page.title()}`)
    console.log(`  URL: ${page.url()}`)
    
    // 获取页面的一些基本信息
    const userAgent = await page.evaluate(() => navigator.userAgent)
    console.log(`  User Agent: ${userAgent}`)
    
    // 获取页面尺寸
    const viewport = page.viewportSize()
    console.log(`  视口尺寸: ${viewport ? `${viewport.width}x${viewport.height}` : '未设置'}`)
  })
}

/**
 * @description 示例：连接到浏览器并导航到新页面
 */
async function exampleNavigateToPage(url = 'https://www.example.com') {
  console.log(`\n=== 示例：导航到 ${url} ===`)
  
  await connectAndExecute(null, async (browser, context, page) => {
    console.log(`🚀 正在导航到: ${url}`)
    await page.goto(url, { waitUntil: 'domcontentloaded' })
    
    console.log(`✅ 导航完成`)
    console.log(`  新标题: ${await page.title()}`)
    console.log(`  新URL: ${page.url()}`)
  })
}

/**
 * @description 示例：连接到浏览器并截图
 */
async function exampleTakeScreenshot(filename = 'screenshot.png') {
  console.log(`\n=== 示例：截图保存为 ${filename} ===`)
  
  await connectAndExecute(null, async (browser, context, page) => {
    console.log('📸 正在截图...')
    await page.screenshot({ path: filename, fullPage: true })
    console.log(`✅ 截图已保存: ${filename}`)
  })
}

// 主函数
async function main() {
  // 示例1: 获取页面信息
  await exampleGetPageInfo()
  
  // 示例2: 导航到新页面（取消注释来测试）
  // await exampleNavigateToPage('https://www.baidu.com')
  
  // 示例3: 截图（取消注释来测试）
  // await exampleTakeScreenshot('bitbrowser_screenshot.png')
}

// 运行示例
if (require.main === module) {
  main().catch(console.error)
}

module.exports = {
  connectToLocalBrowser,
  connectAndExecute,
  exampleGetPageInfo,
  exampleNavigateToPage,
  exampleTakeScreenshot
}
