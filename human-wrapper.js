// @ts-check

/**
 * @typedef {import('playwright').Page} Page
 * @typedef {import('playwright').Locator} Locator
 */

const defaultTimeout = 10000;

/**
 * 包装 Locator，增强功能：
 * - 增加 humanClick / humanType
 * - 自动为常用方法添加默认 timeout（可被用户参数覆盖）
 * - 支持链式方法（如 first/nth/locator）递归封装
 * @param {Locator} locator
 * @returns {Locator & { humanClick: () => Promise<void>, humanType: (text: string) => Promise<void> }}
 */
function wrapLocator(locator) {
  const handler = {
    get(target, prop) {
      if (prop === 'humanClick') {
        return async (delayMs = 1000) => {
          await target.click({ timeout: defaultTimeout });
          await new Promise(res => setTimeout(res, delayMs));
        };
      }

      if (prop === 'humanType') {
        return async (text) => {
          await target.fill('', { timeout: defaultTimeout });
          for (const char of text) {
            await target.type(char, {
              delay: Math.random() * 150 + 50,
              timeout: defaultTimeout,
            });
          }
        };
      }

      // 自动补上 timeout 的方法
      const timeoutMethods = ['click', 'fill', 'type', 'isVisible', 'waitFor', 'waitForElementState', 'count'];
      if (timeoutMethods.includes(prop)) {
        return (...args) => {
          const last = args[args.length - 1];
          if (
            typeof last !== 'object' ||
            last === null ||
            !('timeout' in last)
          ) {
            args.push({ timeout: defaultTimeout });
          }
          return target[prop](...args);
        };
      }

      // 链式方法递归封装
      const chainable = ['first', 'last', 'nth', 'locator', 'filter'];
      if (chainable.includes(prop)) {
        return (...args) => wrapLocator(target[prop](...args));
      }

      return target[prop];
    },
  };

  return new Proxy(locator, handler);
}

/**
 * 包装 Page，使 getByXXX 方法返回增强的 Locator（支持 human 方法 + 默认 timeout）
 * @param {Page} page
 * @returns {Page}
 */
function createHumanPageWrapper(page) {
  return new Proxy(page, {
    get(target, prop) {
      const locatorMethods = [
        'getByRole',
        'getByLabel',
        'getByText',
        'getByPlaceholder',
        'getByTitle',
        'getByAltText',
        'getByTestId'
      ];

      if (locatorMethods.includes(prop)) {
        return (...args) => {
          const locator = target[prop](...args);
          return wrapLocator(locator);
        };
      }

      return target[prop];
    },
  });
}

module.exports = {
  createHumanPageWrapper,
};
