/**
 * @description nodejs dmeo，仅作为demo使用，连接浏览器后，任何业务请直接查询 puppeteer 或 selenium 文档
 * @description 中文文档：https://doc2.bitbrowser.cn/jiekou/ben-di-fu-wu-zhi-nan.html
 * @description 英文文档：https://doc.bitbrowser.net/api-docs/introduction
 * */

const {
  openBrowser,
  closeBrowser,
  createBrowser,
  deleteBrowser,
  getBrowserDetail,
  addGroup,
  editGroup,
  deleteGroup,
  getGroupDetail,
  getGroupList,
  getBrowserList,
  getAllBrowserList,
  getLocalOpenBrowsersDetail,
  getPids,
  updatepartial,
  updateBrowserMemark,
  deleteBatchBrowser,
  getBrowserConciseList,
  getAlivePids,
  getAliveBrowsersPids,
  batchUpdateBrowserGroup,
  closeBrowsersBySeqs,
  batchUpdateProxy
} = require('./request')
const puppeteer = require('puppeteer')
// 主程序
main()

async function main() {
    // 使用新的方法获取所有窗口列表
    // const allBrowsers = await getAllBrowserList()
    // console.log(`总共找到 ${allBrowsers.length} 个浏览器窗口`)

    // 获取本地打开的窗口详细信息
    const localOpenResult = await getLocalOpenBrowsersDetail()
    // console.log(`\n本地打开窗口信息:`, localOpenResult)
    //打印本地窗口基本信息
    if (localOpenResult.success) {
        console.log(`找到 ${localOpenResult.count} 个本地打开的窗口`)
        localOpenResult.localOpenBrowsers.forEach(browser => {
            console.log(`窗口: ${browser.name}, SEQ: ${browser.seq},ID: ${browser.id}`)
        })
    } else {
        console.error('❌ 获取本地打开窗口失败:', localOpenResult.error)
    }

    //拿到第一个窗口并用 playwrige 连上去
    const firstBrowser = localOpenResult.localOpenBrowsers[0]
    if (firstBrowser) {
        const res = await openBrowser({
            id: firstBrowser.id,
            args: [],
            loadExtensions: false,
            extractIp: false
        })
        if (res.success) {
            const browser = await puppeteer.connect({
                browserWSEndpoint: res.data.ws,
                defaultViewport: null
            })
            const pages = await browser.pages()
            console.log('pages length ===>>> ', pages.length)
            await sleep(5000)
        } else {
            console.error('浏览器打开失败')
        }
    }

    // // 统计不同状态的窗口数量
    // let activeCount = 0
    // let inactiveCount = 0
    // let localOpenCount = localOpenResult.success ? localOpenResult.count : 0
    // let activeButNotLocalCount = 0

    // // 获取本地打开的窗口ID列表
    // const localOpenBrowserIds = localOpenResult.success ?
    //   localOpenResult.localOpenBrowsers.map(b => b.id) : []

    // console.log('\n窗口详情:')

    // // 先显示本地打开的窗口（无论状态如何）
    // if (localOpenResult.success && localOpenResult.localOpenBrowsers.length > 0) {
    //   console.log('\n🔥 本地打开的窗口:')
    //   for(let browser of localOpenResult.localOpenBrowsers) {
    //     console.log(`✅ [本地打开] ID: ${browser.id}, 名称: ${browser.name}, 状态: ${browser.status}, PID: ${browser.pid}`)
    //   }
    // } else {
    //   console.log('\n🔥 本地打开的窗口: 无')
    // }

    // // 然后显示其他活跃窗口
    // console.log('\n📋 其他活跃窗口:')
    // for(let one of allBrowsers){
    //   const isLocalOpen = localOpenBrowserIds.includes(one.id)

    //   if(one.status === 1){
    //     if (!isLocalOpen) {
    //       console.log(`⚠️  [仅活跃] ID: ${one.id}, 名称: ${one.name}, 状态: ${one.status}`)
    //       activeButNotLocalCount++
    //     }
    //     activeCount++
    //   }
    //   else{
    //     // console.log(`❌ [非活跃] 状态: ${one.status}`)
    //     inactiveCount++
    //   }
    // }

    // console.log(`\n=== 统计结果 ===`)
    // console.log(`总窗口数: ${allBrowsers.length} 个`)
    // console.log(`活跃窗口: ${activeCount} 个`)
    // console.log(`  ├─ 本地打开: ${localOpenCount} 个`)
    // console.log(`  └─ 仅活跃(未本地打开): ${activeButNotLocalCount} 个`)
    // console.log(`非活跃窗口: ${inactiveCount} 个`)
}
