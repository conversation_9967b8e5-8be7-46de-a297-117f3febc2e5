const readline = require('readline');
const { createHumanPageWrapper } = require('./human-wrapper');
const { getLocalOpenBrowsersDetail, getBrowserWebSocketEndpoint } = require('./request');
const { chromium } = require('playwright');
const { getNextAvailableCard, markCardResult } = require('./card-manager');

main();

var g_pages = [];
global.g_pages = g_pages;

async function main() {
  const localOpenResult = await getLocalOpenBrowsersDetail();
  if (!localOpenResult.success) {
    console.error('❌ 获取本地打开窗口失败:', localOpenResult.error);
    return;
  }

  const allBrowsers = localOpenResult.localOpenBrowsers;
  console.log(`找到 ${allBrowsers.length} 个本地打开的窗口：`);
  for (const browser of allBrowsers) {
    console.log(`➡️ 窗口名: ${browser.name}, SEQ: ${browser.seq}, ID: ${browser.id}`);
  }

  // 获取用户输入
  const selectedSeqList = await askUserWindowSeq();

  // 过滤要处理的窗口
  const filteredBrowsers = selectedSeqList.includes('1')
    ? allBrowsers
    : allBrowsers.filter(browser => selectedSeqList.includes(String(browser.seq)));

  if (filteredBrowsers.length === 0) {
    console.log('❗没有匹配的窗口，程序结束。');
    return;
  }

  // 并行处理这些窗口
//   const tasks = filteredBrowsers.map(browser => handleBrowserWindow(browser));
//   await Promise.all(tasks);
    function delayedTask(taskFn, delayMs) {
    return new Promise(resolve => {
        setTimeout(async () => {
        try {
            await taskFn();
        } catch (err) {
            console.error('任务出错:', err.message);
        }
        resolve();
        }, delayMs);
    });
    }

    const tasks = filteredBrowsers.map((browser, index) => {
    const delay = index * 1000; // 每个窗口延迟启动1秒
    return delayedTask(() => handleBrowserWindow(browser), delay);
    });

    await Promise.all(tasks); // 所有任务并发执行，但有延迟启动

    //任务执行关闭，关闭程序
    process.exit(0);
}

async function askUserWindowSeq() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(`请输入要操作的窗口 SEQ（如 1 表示全部，或 窗口序号以，分割）：`, answer => {
      rl.close();
      const input = answer.trim();
      if (input === '1') {
        resolve(['1']);
      } else {
        const seqList = input.split(',').map(seq => seq.trim()).filter(Boolean);
        resolve(seqList);
      }
    });
  });
}

async function handleBrowserWindow(browser) {
  try {
    // 取卡（自动循环，跳过失败卡）
    const card = getNextAvailableCard();

    const out = await getBrowserWebSocketEndpoint(browser.id);
    console.log(`✅ 处理窗口: ${browser.name} (SEQ: ${browser.seq})`);

    const playwrightBrowser = await chromium.connectOverCDP(out.wsEndpoint);
    const contexts = playwrightBrowser.contexts();

    let page = null;
    for (const context of contexts) {
      const pages = context.pages();
      for (const one of pages) {
        try {
          let title = await one.title();
          if (title.includes('账单与支付')) {
            await one.bringToFront();
            page = one;
            break;
          }
        } catch {}
      }

      if (!page) {
        page = await context.newPage();
        await page.goto('https://business.facebook.com/ads/manager/account_settings/account_billing');
      }
      g_pages.push(page);
    }

    const pageHuman = createHumanPageWrapper(page);
    if(await pageHuman.getByText('营业地点和货币').isVisible({ timeout: 0 }) || await pageHuman.getByText('借记卡或信用卡').isVisible({ timeout: 0 })){
        await pageHuman.getByLabel('关闭').humanClick();
    }

    //添加支付信息  弹窗已经打开
    if(await pageHuman.getByText('添加支付信息').isVisible()){
        console.log('有');
        await selCountryMoney(pageHuman);
        
    }
    else if(await pageHuman.getByText('借记卡或信用卡').isVisible()){
        console.log('借记卡或信用卡 页面打开了');
    }
    else{
        console.log('没有');
        await pageHuman.getByRole('button', { name: '添加支付方式' }).humanClick();
        await selCountryMoney(pageHuman);
    }

    

    // await pageHuman.getByRole('button', { name: '继续' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '持卡人姓名' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '持卡人姓名' }).humanType('123');
    await pageHuman.getByRole('textbox', { name: '卡号' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '卡号' }).humanType(card.cardNumber);
    await pageHuman.getByRole('textbox', { name: 'MM/YY' }).humanClick();
    await pageHuman.getByRole('textbox', { name: 'MM/YY' }).humanType(card.expire);
    await pageHuman.getByRole('textbox', { name: '安全码' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '安全码' }).humanType(card.cvv);

    await pageHuman.getByRole('button', { name: '保存' }).humanClick();

    result = '等待'
    try {
        const successLocator = pageHuman.getByText('成功');
        const failLocator = pageHuman.getByText('出错了');

        const winner = await Promise.race([
            successLocator.waitFor({ state: 'visible', timeout: 30000 }).then(() => '成功'),
            failLocator.waitFor({ state: 'visible', timeout: 30000 }).then(() => '失败'),
        ]);

        result = winner;
    } catch (err) {
        console.log('⚠️ 未检测到成功或失败提示，可能超时或页面无响应',err);
    }
    if(result == '成功'){
      console.log('绑卡成功');
      //如果有公司信息则需要填写
      if(await pageHuman.getByRole('textbox', { name: '市/镇' }).count()>0){
        await pageHuman.getByRole('textbox', { name: '市/镇' }).humanType('abccc')
        await pageHuman.getByRole('combobox', { name: '所在州（省）' }).locator('i').humanClick();
        await pageHuman.getByRole('option', { name: '阿拉巴马州' }).humanClick();
        await pageHuman.getByRole('textbox', { name: '邮编' }).humanClick();
        await pageHuman.getByRole('textbox', { name: '邮编' }).humanType('10001');
        await pageHuman.getByRole('button', { name: '保存' }).humanClick();
      }
      else{
        console.log('没有公司信息');
        await pageHuman.getByRole('button', { name: '完成' }).humanClick();
      }

    }
    else if(result == '失败'){
      console.log('绑卡失败');
    }
    

    markCardResult(card.index, result);
    console.log('结束');
  } catch (error) {
    console.error(`❌ 处理窗口 ${browser.name} 时出错:`, error.message);
  }
}

//选择所在地和货币
async function selCountryMoney(pageHuman) {
  // 处理“营业地点和货币”弹窗
  try {
    await pageHuman.getByText('营业地点和货币').waitFor({ state: 'visible', timeout: 1000 });
    console.log('关闭弹窗');
    await pageHuman.getByRole('button', { name: '继续' }).humanClick();
  } catch (e) {
    console.log('未出现【营业地点和货币】，跳过');
  }

  // 处理“选择所在地和货币”
  try {
    await pageHuman.getByText('选择所在地和货币').waitFor({ state: 'visible', timeout: 1000 });
    console.log('选择所在地');
    await pageHuman.getByRole('combobox', { name: '国家/地区' }).locator('i').humanClick();
    await pageHuman.getByRole('option', { name: '美国', exact: true }).humanClick();
    await pageHuman.getByRole('combobox', { name: '货币' }).locator('i').humanClick();
    await pageHuman.getByRole('option', { name: '欧元' }).humanClick();
    await pageHuman.getByRole('button', { name: '继续' }).humanClick();
    await pageHuman.waitForTimeout(1000);
    await pageHuman.getByRole('button', { name: '继续' }).humanClick();
  } catch (e) {
    console.log('未出现【选择所在地和货币】，跳过');
  }

try {
    if(await pageHuman.locator('div[role="dialog"]').getByText('添加支付方式').count()>0){
      await pageHuman.getByRole('button', { name: '继续' }).first().humanClick();
    }

  } catch (e) {
    console.log('未出现【添加支付方式才能开启自动付费功能】，跳过');
  }
}
