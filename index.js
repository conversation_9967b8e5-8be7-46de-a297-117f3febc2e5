/**
 * @description nodejs dmeo，仅作为demo使用，连接浏览器后，任何业务请直接查询 puppeteer 或 selenium 文档
 * @description 中文文档：https://doc2.bitbrowser.cn/jiekou/ben-di-fu-wu-zhi-nan.html
 * @description 英文文档：https://doc.bitbrowser.net/api-docs/introduction
 * */

const {
  openBrowser,
  closeBrowser,
  createBrowser,
  deleteBrowser,
  getBrowserDetail,
  addGroup,
  editGroup,
  deleteGroup,
  getGroupDetail,
  getGroupList,
  getBrowserList,
  getAllBrowserList,
  getLocalOpenBrowsersDetail,
  getPids,
  updatepartial,
  updateBrowserMemark,
  deleteBatchBrowser,
  getBrowserConciseList,
  getAlivePids,
  getAliveBrowsersPids,
  batchUpdateBrowserGroup,
  closeBrowsersBySeqs,
  batchUpdateProxy,
  getBrowserWebSocketEndpoint
} = require('./request')
const puppeteer = require('puppeteer')
// 主程序
main()

async function main() {
    // 获取本地打开的窗口详细信息
    const localOpenResult = await getLocalOpenBrowsersDetail()
    //打印本地窗口基本信息
    if (localOpenResult.success) {
        console.log(`找到 ${localOpenResult.count} 个本地打开的窗口`)
        localOpenResult.localOpenBrowsers.forEach(async browser => {
            let out = await getBrowserWebSocketEndpoint(browser.id)
            console.log(`窗口: ${browser.name}, SEQ: ${browser.seq},ID: ${browser.id},WS: ${out.wsEndpoint}`)
            //playwriget 连上去并打开网页 www.facebook.com
            const ww = await puppeteer.connect({
                browserWSEndpoint: out.wsEndpoint,
                defaultViewport: null
            })
            const pages = await ww.pages()
            //打印所有页面标题
            pages.forEach(async page => {
                console.log(`页面: ${await page.title()}`)
                if(page.title.indexOf())
            })

        })
    } else {
        console.error('❌ 获取本地打开窗口失败:', localOpenResult.error)
    }

}
