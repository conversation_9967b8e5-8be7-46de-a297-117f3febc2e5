const readline = require('readline');
const { createHumanPageWrapper } = require('./human-wrapper');
const { getLocalOpenBrowsersDetail, getBrowserWebSocketEndpoint } = require('./request');
const { chromium } = require('playwright');
const { getNextAvailableCard, markCardResult } = require('./card-manager');

// 全局变量
var g_pages = [];
global.g_pages = g_pages;

/**
 * @description 创建带窗口 seq 编号的日志器
 * @param {number|string} seq 窗口序号
 * @returns {object} 日志器对象
 */
function createLogger(seq) {
    const prefix = `【${seq}】`

    function logger(level, message, ...args) {
        const fullMessage = `${prefix}: ${message}`

        switch (level) {
            case 'info':
                console.info(fullMessage, ...args)
                break
            case 'warn':
                console.warn(fullMessage, ...args)
                break
            case 'error':
                console.error(fullMessage, ...args)
                break
            case 'log':
            default:
                console.log(fullMessage, ...args)
                break
        }
    }

    return {
        info: (message, ...args) => logger('info', message, ...args),
        warn: (message, ...args) => logger('warn', message, ...args),
        error: (message, ...args) => logger('error', message, ...args),
        log: (message, ...args) => logger('log', message, ...args),
        debug: (message, ...args) => logger('log', `[DEBUG] ${message}`, ...args)
    }
}

main();

async function main() {
  const localOpenResult = await getLocalOpenBrowsersDetail();
  if (!localOpenResult.success) {
    console.error('❌ 获取本地打开窗口失败:', localOpenResult.error);
    return;
  }

  const allBrowsers = localOpenResult.localOpenBrowsers;
  console.log(`找到 ${allBrowsers.length} 个本地打开的窗口：`);
  for (const browser of allBrowsers) {
    console.log(`➡️ 窗口名: ${browser.name}, SEQ: ${browser.seq}, ID: ${browser.id}`);
  }

  // 获取用户输入
  const selectedSeqList = await askUserWindowSeq();

  // 过滤要处理的窗口
  const filteredBrowsers = selectedSeqList.includes('1')
    ? allBrowsers
    : allBrowsers.filter(browser => selectedSeqList.includes(String(browser.seq)));

  if (filteredBrowsers.length === 0) {
    console.log('❗没有匹配的窗口，程序结束。');
    return;
  }

  // 并行处理这些窗口
//   const tasks = filteredBrowsers.map(browser => handleBrowserWindow(browser));
//   await Promise.all(tasks);
    function delayedTask(taskFn, delayMs) {
    return new Promise(resolve => {
        setTimeout(async () => {
        try {
            await taskFn();
        } catch (err) {
            console.error('任务出错:', err.message);
        }
        resolve();
        }, delayMs);
    });
    }

    const tasks = filteredBrowsers.map((browser, index) => {
    const delay = index * 1000; // 每个窗口延迟启动1秒
    return delayedTask(() => handleBrowserWindow(browser), delay);
    });

    await Promise.all(tasks); // 所有任务并发执行，但有延迟启动

    //任务执行关闭，关闭程序
    process.exit(0);
}

async function askUserWindowSeq() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    rl.question(`请输入要操作的窗口 SEQ（如 1 表示全部，或 窗口序号以，分割）：`, answer => {
      rl.close();
      const input = answer.trim();
      if (input === '1') {
        resolve(['1']);
      } else {
        const seqList = input.split(',').map(seq => seq.trim()).filter(Boolean);
        resolve(seqList);
      }
    });
  });
}

async function handleBrowserWindow(browser) {
  try {
    // 为当前窗口创建独立的日志器
    const log = createLogger(browser.seq);

    // 取卡（自动循环，跳过失败卡）
    const card = getNextAvailableCard();

    const out = await getBrowserWebSocketEndpoint(browser.id);
    log.info(`✅ 处理窗口: ${browser.name} (SEQ: ${browser.seq})`);

    const playwrightBrowser = await chromium.connectOverCDP(out.wsEndpoint);
    const contexts = playwrightBrowser.contexts();

    let page = null;
    for (const context of contexts) {
      const pages = context.pages();
      for (const one of pages) {
        try {
          let title = await one.title();
          if (title.includes('账单与支付')) {
            await one.bringToFront();
            page = one;
            break;
          }
        } catch {}
      }

      if (!page) {
        page = await context.newPage();
        await page.goto('https://business.facebook.com/ads/manager/account_settings/account_billing');
      }
      g_pages.push(page);
    }

    const pageHuman = createHumanPageWrapper(page);
    if(await pageHuman.getByText('营业地点和货币').isVisible({ timeout: 0 }) || await pageHuman.getByText('借记卡或信用卡').isVisible({ timeout: 0 })){
        await pageHuman.getByLabel('关闭').humanClick();
    }

    //添加支付信息  弹窗已经打开
    if(await pageHuman.getByText('添加支付信息').isVisible()){
        log.info('检测到"添加支付信息"弹窗');
        await selCountryMoney(pageHuman, log);

    }
    else if(await pageHuman.getByText('借记卡或信用卡').isVisible()){
        log.info('检测到"借记卡或信用卡"页面已打开');
    }
    else{
        log.info('未检测到支付弹窗，点击"添加支付方式"按钮');
        await pageHuman.getByRole('button', { name: '添加支付方式' }).humanClick();
        await selCountryMoney(pageHuman, log);
    }

    

    // await pageHuman.getByRole('button', { name: '继续' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '持卡人姓名' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '持卡人姓名' }).humanType('123');
    await pageHuman.getByRole('textbox', { name: '卡号' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '卡号' }).humanType(card.cardNumber);
    await pageHuman.getByRole('textbox', { name: 'MM/YY' }).humanClick();
    await pageHuman.getByRole('textbox', { name: 'MM/YY' }).humanType(card.expire);
    await pageHuman.getByRole('textbox', { name: '安全码' }).humanClick();
    await pageHuman.getByRole('textbox', { name: '安全码' }).humanType(card.cvv);

    await pageHuman.getByRole('button', { name: '保存' }).humanClick();

    result = '等待'
    try {
        const successLocator = pageHuman.getByText('成功');
        const failLocator = pageHuman.getByText('出错了');

        const winner = await Promise.race([
            successLocator.waitFor({ state: 'visible', timeout: 30000 }).then(() => '成功'),
            failLocator.waitFor({ state: 'visible', timeout: 30000 }).then(() => '失败'),
        ]);

        result = winner;
    } catch (err) {
        log.warn('⚠️ 未检测到成功或失败提示，可能超时或页面无响应', err.message);
    }
    if(result == '成功'){
      log.info('✅ 绑卡成功');
      //如果有公司信息则需要填写
      if(await pageHuman.getByRole('textbox', { name: '市/镇' }).count()>0){
        log.info('检测到公司信息表单，开始填写');
        await pageHuman.getByRole('textbox', { name: '市/镇' }).humanType('abccc')
        await pageHuman.getByRole('combobox', { name: '所在州（省）' }).locator('i').humanClick();
        await pageHuman.getByRole('option', { name: '阿拉巴马州' }).humanClick();
        await pageHuman.getByRole('textbox', { name: '邮编' }).humanClick();
        await pageHuman.getByRole('textbox', { name: '邮编' }).humanType('10001');
        await pageHuman.getByRole('button', { name: '保存' }).humanClick();
        log.info('公司信息填写完成');
      }
      else{
        log.info('无需填写公司信息，直接完成');
        await pageHuman.getByRole('button', { name: '完成' }).humanClick();
      }

    }
    else if(result == '失败'){
      log.error('❌ 绑卡失败');
    }


    markCardResult(card.index, result);
    log.info('🏁 窗口处理结束');
  } catch (error) {
    const log = createLogger(browser.seq);
    log.error(`❌ 处理窗口 ${browser.name} 时出错: ${error.message}`);
  }
}

//选择所在地和货币
async function selCountryMoney(pageHuman, log) {
  // 处理“营业地点和货币”弹窗
  try {
    await pageHuman.getByText('营业地点和货币').waitFor({ state: 'visible', timeout: 1000 });
    log.info('检测到"营业地点和货币"弹窗，点击继续');
    await pageHuman.getByRole('button', { name: '继续' }).humanClick();
  } catch (e) {
    log.debug('未出现【营业地点和货币】弹窗，跳过');
  }

  // 处理“选择所在地和货币”
  try {
    await pageHuman.getByText('选择所在地和货币').waitFor({ state: 'visible', timeout: 1000 });
    log.info('开始选择所在地和货币');
    await pageHuman.getByRole('combobox', { name: '国家/地区' }).locator('i').humanClick();
    await pageHuman.getByRole('option', { name: '美国', exact: true }).humanClick();
    log.debug('已选择国家: 美国');
    await pageHuman.getByRole('combobox', { name: '货币' }).locator('i').humanClick();
    await pageHuman.getByRole('option', { name: '欧元' }).humanClick();
    log.debug('已选择货币: 欧元');
    await pageHuman.getByRole('button', { name: '继续' }).humanClick();
    await pageHuman.waitForTimeout(1000);
    await pageHuman.getByRole('button', { name: '继续' }).humanClick();
    log.info('所在地和货币选择完成');
  } catch (e) {
    log.debug('未出现【选择所在地和货币】弹窗，跳过');
  }

try {
    if(await pageHuman.locator('div[role="dialog"]').getByText('添加支付方式').count()>0){
      log.info('检测到"添加支付方式"对话框，点击继续');
      await pageHuman.getByRole('button', { name: '继续' }).first().humanClick();
    }

  } catch (e) {
    log.debug('未出现【添加支付方式才能开启自动付费功能】弹窗，跳过');
  }
}
