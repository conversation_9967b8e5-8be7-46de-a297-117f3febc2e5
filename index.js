/**
 * @description nodejs demo，仅作为demo使用，连接浏览器后，任何业务请直接查询 playwright 文档
 * @description 中文文档：https://doc2.bitbrowser.cn/jiekou/ben-di-fu-wu-zhi-nan.html
 * @description 英文文档：https://doc.bitbrowser.net/api-docs/introduction
 * @description Playwright文档：https://playwright.dev/docs/intro
 * */

const {
  getLocalOpenBrowsersDetail,
  getBrowserWebSocketEndpoint
} = require('./request')
const { chromium } = require('playwright')
// 主程序
main()

var g_pages = []

async function main() {
    // 获取本地打开的窗口详细信息
    const localOpenResult = await getLocalOpenBrowsersDetail()
    //打印本地窗口基本信息
    if (localOpenResult.success) {
        console.log(`找到 ${localOpenResult.count} 个本地打开的窗口`)

        // 使用 for...of 循环来正确处理异步操作
        for (const browser of localOpenResult.localOpenBrowsers) {
            try {
                let out = await getBrowserWebSocketEndpoint(browser.id)
                console.log(`窗口: ${browser.name}, SEQ: ${browser.seq}, ID: ${browser.id}, WS: ${out.wsEndpoint}`)

                // 使用 Playwright 连接到浏览器
                const playwrightBrowser = await chromium.connectOverCDP(out.wsEndpoint)

                // 获取所有上下文
                const contexts = playwrightBrowser.contexts()
                console.log(`找到 ${contexts.length} 个浏览器上下文`)
                 let page = null;
                // 遍历所有上下文和页面
                for (const context of contexts) {
                    const pages = context.pages()
                    console.log(`上下文中有 ${pages.length} 个页面`)

                    // 打印所有页面标题
                    for (const one of pages) {
                        try {
                            let title = await one.title()
                            console.log(`页面: ${title}`)

                            if (title.indexOf('账单与支付') >= 0) {
                                // 当前 tab 激活
                                await one.bringToFront()
                            }
                            page  = one
                        } catch (pageError) {
                            console.error(`获取页面标题失败:`, pageError.message)
                        }
                    }
                    if(page == null) //没有找到，打开新的
                    {
                        page = await context.newPage()
                        await page.goto('https://business.facebook.com/ads/manager/account_settings/account_billing')
                    }
                    
                }

                //找到添加支付按钮并点击
                // await page.waitForSelector('text=添加支付方法')
                // await page.click('text=添加支付方法')
                page.getByRole('button', { name: '添加支付方式' }).count()
                await page.pause()


            } catch (error) {
                console.error(`❌ 处理窗口 ${browser.name} 时出错:`, error.message)
            }
        }
    } else {
        console.error('❌ 获取本地打开窗口失败:', localOpenResult.error)
    }
}
