/**
 * @description nodejs dmeo，仅作为demo使用，连接浏览器后，任何业务请直接查询 puppeteer 或 selenium 文档
 * @description 中文文档：https://doc2.bitbrowser.cn/jiekou/ben-di-fu-wu-zhi-nan.html
 * @description 英文文档：https://doc.bitbrowser.net/api-docs/introduction
 * */

const {
  openBrowser,
  closeBrowser,
  createBrowser,
  deleteBrowser,
  getBrowserDetail,
  addGroup,
  editGroup,
  deleteGroup,
  getGroupDetail,
  getGroupList,
  getBrowserList,
  getPids,
  updatepartial,
  updateBrowserMemark,
  deleteBatchBrowser,
  getBrowserConciseList,
  getAlivePids,
  getAliveBrowsersPids,
  batchUpdateBrowserGroup,
  closeBrowsersBySeqs,
  batchUpdateProxy
} = require('./request')
const puppeteer = require('puppeteer')
// 主程序
main()

/**
 * @description 获取所有浏览器窗口列表（自动分页）
 * @param {Object} options 查询选项
 * @param {String} options.groupId 分组ID，非必传
 * @param {String} options.name 窗口名称，用于模糊查询，非必传
 * @param {String} options.sortProperties 排序参数，默认序号，seq，非必传
 * @param {String} options.sortDirection 排序顺序参数，默认desc，可传asc，非必传
 * @returns {Promise<Array>} 返回所有浏览器窗口列表
 */
async function getAllBrowserList(options = {}) {
  const allBrowsers = []
  let page = 1
  const pageSize = 100 // 比特浏览器单次最大查询数量
  let hasMore = true

  while (hasMore) {
    try {
      const response = await getBrowserList({
        page,
        pageSize,
        ...options
      })

      if (response && response.data && response.data.list) {
        allBrowsers.push(...response.data.list)

        // 检查是否还有更多数据
        // 如果返回的数据少于pageSize，说明已经是最后一页
        hasMore = response.data.list.length === pageSize
        page++

        console.log(`已获取第 ${page - 1} 页，共 ${response.data.list.length} 个窗口`)
      } else {
        console.log('获取浏览器列表失败或数据格式异常')
        hasMore = false
      }
    } catch (error) {
      console.error(`获取第 ${page} 页数据时出错:`, error.message)
      hasMore = false
    }
  }

  console.log(`总共获取到 ${allBrowsers.length} 个浏览器窗口`)
  return allBrowsers
}

async function main() {
    // 使用新的方法获取所有窗口列表
    const allBrowsers = await getAllBrowserList()

    console.log(`总共找到 ${allBrowsers.length} 个浏览器窗口`)

    // 统计不同状态的窗口数量
    let activeCount = 0
    let inactiveCount = 0

    for(let one of allBrowsers){
      if(one.status === 1){
        console.log(one.status, one.id, one.name)
        activeCount++
      }
      else{
        console.log(one.status)
        inactiveCount++
      }
    }

    console.log(`\n统计结果:`)
    console.log(`活跃窗口: ${activeCount} 个`)
    console.log(`非活跃窗口: ${inactiveCount} 个`)
}
