const axios = require('axios').default

const baseURL = 'http://127.0.0.1:54345'

const request = axios.create({
  baseURL,
  timeout: 0
})

request.interceptors.response.use(
  response => {
    if (response.status === 200) {
      return response.data
    } else {
      console.log('请求失败，检查网络')
    }
  },
  error => {
    console.error('请求失败了')
    return Promise.reject(error)
  }
)

/**
 * @description 打开浏览器并返回wspoint
 * @returns {Promise<Object>} Promise<any>
 * @param {Object} data
 * @param {String} data.id 窗口id
 * @param {Array} data.args 启动附加参数，数组类型，比如 ["--headless"]
 * @param {Boolean} data.loadExtensions 是否加载扩展
 * @param {Boolean} data.extractIp 是否尝试提取IP
 */
function openBrowser(data) {
  return request({ method: 'post', url: '/browser/open', data })
}

/**
 * @description 关闭浏览器
 * @param {String} id
 * @returns {Promise}
 */
function closeBrowser(id) {
  return request({ method: 'post', url: '/browser/close', data: { id } })
}

/**
 * @description 创建浏览器
 * @param {Object} data
 * @returns {Promise}
 */
function createBrowser(data) {
  return request({ method: 'post', url: '/browser/update', data })
}
/**
 * @description 修改browser信息，只修改传入的配置
 * @param {Object} data 参考创建配置项
 * @returns {Promise}
 */
function updatepartial(data) {
  return request({ method: 'post', url: '/browser/update/partial', data })
}
/**
 * @description 批量删除浏览器
 * @param {array} ids
 */
function deleteBatchBrowser(ids) {
  return request({ method: 'post', url: '/browser/delete/ids', data: { ids } })
}

/**
 * @description 删除浏览器
 * @param {String} id
 * @returns {Promise}
 */
function deleteBrowser(id) {
  return request({ method: 'post', url: '/browser/delete', data: { id } })
}

/**
 * @description 获取浏览器详情
 * @param {String} id
 * @returns {Promise}
 * */
function getBrowserDetail(id) {
  return request({ method: 'post', url: '/browser/detail', data: { id } })
}

/**
 * @description 获取浏览器列表
 * @param {Object} data
 * @param {Number} data.page // 必传
 * @param {Number} data.pageSize // 必传
 * @param {String} data.groupId // 分组ID，非必传
 * @param {String} data.name // 窗口名称，用于模糊查询，非必传
 * @param {String} data.sortProperties // 排序参数，默认序号，seq，非必传
 * @param {String} data.sortDirection // 排序顺序参数，默认desc，可传asc，非必传
 * @returns {Promise}
 * */
function getBrowserList(data) {
  return request({ method: 'post', url: '/browser/list', data })
}
/**
 * @description 获取浏览器列表（简洁）
 */
function getBrowserConciseList(data) {
  return request({ method: 'post', url: '/browser/list/concise', data })
}
/**
 * @description 分组list
 * @param {Number} page 从0开始
 * @param {Number} pageSize 例如10
 * @returns {Promise}
 * */
function getGroupList(page, pageSize) {
  return request({ method: 'post', url: '/group/list', data: { page, pageSize } })
}

/**
 * @description 添加分组
 * @param {String} groupName
 * @param {Number} sortNum
 * @returns {Promise}
 * */
function addGroup(groupName, sortNum) {
  return request({ method: 'post', url: '/group/add', data: { groupName, sortNum } })
}

/**
 * @description 修改分组
 * @param {String} id
 * @param {String} groupName
 * @param {Number} sortNum
 * @returns {Promise}
 * */
function editGroup(id, groupName, sortNum) {
  return request({ method: 'post', url: '/group/edit', data: { id, groupName, sortNum } })
}

/**
 * @description 删除分组
 * @param {String} id
 * @returns {Promise}
 * */
function deleteGroup(id) {
  return request({ method: 'post', url: '/group/delete', data: { id } })
}

/**
 * @description 分组详情
 * @param {String} id
 * */
function getGroupDetail(id) {
  return request({ method: 'post', url: '/group/detail', data: { id } })
}

/**
 * @description 获取指定窗口的pids
 * @param {Array} ids
 * @returns
 */
function getPids(ids) {
  return request({ url: '/browser/pids', method: 'post', data: { ids } })
}
/**
 * @description 获取活着窗口的pids
 * @param {Array} ids
 * @returns
 */
function getAlivePids(ids) {
  return request({ url: '/browser/pids/alive', method: 'post', data: { ids } })
}
/**
 * @description 获取所有活着窗口的pids
 * @returns
 */
function getAliveBrowsersPids() {
  return request({ url: '/browser/pids/all', method: 'post' })
}

/**
 * @description 批量修改窗口备注
 * @param {String} remark
 * @param {Array} browserIds
 * @returns
 */
function updateBrowserMemark(remark, browserIds) {
  return request({ url: '/browser/remark/update', method: 'post', data: { remark, browserIds } })
}
/**
 * @description 批量修改窗口分组
 * @param {Object} data
 * @param {Number} data.groupId
 * @param {Array} data.browserIds
 */
function batchUpdateBrowserGroup(data) {
  return request({ url: '/browser/group/update', method: 'post', data })
}
/**
 * @description 通过序号批量关闭浏览器
 *
 */
function closeBrowsersBySeqs(seqs) {
  return request({ url: '/browser/close/byseqs', method: 'post', data: { seqs } })
}
/**
 * @description 批量修改代理
 * @param {Object} data
 * @param {Number} data.proxyMethod //代理类型，2自定义代理，3提取IP
 * @param {String} data.proxyType // 自定义代理类型
 */
function batchUpdateProxy(data) {
  return request({ url: '/browser/proxy/update', method: 'post', data })
}

/**
 * @description 获取所有浏览器窗口列表（自动分页）
 * @param {Object} options 查询选项
 * @param {String} options.groupId 分组ID，非必传
 * @param {String} options.name 窗口名称，用于模糊查询，非必传
 * @param {String} options.sortProperties 排序参数，默认序号，seq，非必传
 * @param {String} options.sortDirection 排序顺序参数，默认desc，可传asc，非必传
 * @returns {Promise<Array>} 返回所有浏览器窗口列表
 */
async function getAllBrowserList(options = {}) {
  const allBrowsers = []
  let page = 1
  const pageSize = 100 // 比特浏览器单次最大查询数量
  let hasMore = true

  while (hasMore) {
    try {
      const response = await getBrowserList({
        page,
        pageSize,
        ...options
      })

      if (response && response.data && response.data.list) {
        allBrowsers.push(...response.data.list)

        // 检查是否还有更多数据
        // 如果返回的数据少于pageSize，说明已经是最后一页
        hasMore = response.data.list.length === pageSize
        page++

        console.log(`已获取第 ${page - 1} 页，共 ${response.data.list.length} 个窗口`)
      } else {
        console.log('获取浏览器列表失败或数据格式异常')
        hasMore = false
      }
    } catch (error) {
      console.error(`获取第 ${page} 页数据时出错:`, error.message)
      hasMore = false
    }
  }

  console.log(`总共获取到 ${allBrowsers.length} 个浏览器窗口`)
  return allBrowsers
}

/**
 * @description 获取已打开窗口的WebSocket端点
 * @param {String} browserId 窗口ID
 * @returns {Promise<Object>} 返回WebSocket连接信息
 */
async function getBrowserWebSocketEndpoint(browserId) {
  try {
    // 注意：这里不调用openBrowser，而是获取已经打开窗口的连接信息
    // 比特浏览器可能需要特殊的API来获取已打开窗口的WebSocket端点
    // 如果没有专门的API，我们可能需要通过其他方式获取

    // 首先检查窗口是否真的在本地打开
    const localOpenResult = await getLocalOpenBrowsersDetail()
    if (!localOpenResult.success) {
      return {
        success: false,
        error: '无法获取本地打开窗口信息'
      }
    }

    const browser = localOpenResult.localOpenBrowsers.find(b => b.id === browserId)
    if (!browser) {
      return {
        success: false,
        error: `窗口 ${browserId} 未在本地打开`
      }
    }

    // 尝试通过PID获取调试端口
    // 比特浏览器通常在启动时会分配一个调试端口
    // 我们需要找到这个端口来构建WebSocket端点

    // 方法1: 尝试常见的调试端口范围
    const commonPorts = [9222, 9223, 9224, 9225, 9226, 9227, 9228, 9229, 9230, 9231, 9232, 9233, 9234, 9235]

    for (let port of commonPorts) {
      try {
        const wsEndpoint = await getWebSocketEndpointFromPort(port, browser.pid)
        if (wsEndpoint) {
          return {
            success: true,
            wsEndpoint,
            port,
            browserId,
            browserName: browser.name,
            pid: browser.pid
          }
        }
      } catch (error) {
        // 继续尝试下一个端口
        continue
      }
    }

    // 方法2: 如果常见端口都不行，尝试通过比特浏览器API获取连接信息
    // 这可能需要调用openBrowser但不实际打开新窗口，只是获取连接信息
    try {
      console.log(`尝试通过比特浏览器API获取窗口 ${browserId} 的连接信息...`)
      // 注意：这里可能会重新激活窗口，但不会创建新窗口
      const openResult = await openBrowser({
        id: browserId,
        args: [],
        loadExtensions: false,
        extractIp: false
      })

      if (openResult && openResult.success && openResult.data && openResult.data.ws) {
        console.log(`通过API获取到WebSocket端点: ${openResult.data.ws}`)
        return {
          success: true,
          wsEndpoint: openResult.data.ws,
          port: 'unknown',
          browserId,
          browserName: browser.name,
          pid: browser.pid,
          method: 'bitbrowser_api'
        }
      }
    } catch (apiError) {
      console.log('通过API获取连接信息失败:', apiError.message)
    }

    return {
      success: false,
      error: `无法找到窗口 ${browserId} 的WebSocket端点`,
      browserId,
      pid: browser.pid
    }

  } catch (error) {
    console.error('获取WebSocket端点失败:', error.message)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * @description 从指定端口获取WebSocket端点
 * @param {Number} port 调试端口
 * @param {Number} expectedPid 期望的进程ID
 * @returns {Promise<String|null>} WebSocket端点URL
 */
async function getWebSocketEndpointFromPort(port, expectedPid) {
  try {
    const axios = require('axios')

    // 获取浏览器版本信息
    const versionResponse = await axios.get(`http://localhost:${port}/json/version`, {
      timeout: 1000
    })

    if (versionResponse.data && versionResponse.data.webSocketDebuggerUrl) {
      // 获取所有页面信息来验证这是正确的浏览器实例
      const pagesResponse = await axios.get(`http://localhost:${port}/json`, {
        timeout: 1000
      })

      if (pagesResponse.data && pagesResponse.data.length > 0) {
        // 返回主要的WebSocket端点
        return versionResponse.data.webSocketDebuggerUrl
      }
    }

    return null
  } catch (error) {
    // 端口不可用或不是正确的浏览器实例
    return null
  }
}

/**
 * @description 获取本地打开的窗口详细信息
 * @returns {Promise<Object>} 返回本地打开窗口的详细信息
 */
async function getLocalOpenBrowsersDetail() {
  try {
    // 获取所有活着窗口的pids
    const alivePidsResponse = await getAliveBrowsersPids()

    if (!alivePidsResponse || !alivePidsResponse.success || !alivePidsResponse.data) {
      return {
        success: false,
        localOpenBrowsers: [],
        pidsInfo: {}
      }
    }

    const pidsInfo = alivePidsResponse.data
    const localOpenBrowserIds = Object.keys(pidsInfo)

    // 获取这些窗口的详细信息
    const localOpenBrowsers = []
    for (let browserId of localOpenBrowserIds) {
      try {
        const browserDetail = await getBrowserDetail(browserId)
        if (browserDetail && browserDetail.success && browserDetail.data) {
          localOpenBrowsers.push({
            ...browserDetail.data,
            pid: pidsInfo[browserId]
          })
        }
      } catch (error) {
        console.error(`获取窗口 ${browserId} 详情失败:`, error.message)
        // 即使获取详情失败，也保留基本信息
        localOpenBrowsers.push({
          id: browserId,
          pid: pidsInfo[browserId],
          name: '获取详情失败',
          status: 'unknown'
        })
      }
    }

    return {
      success: true,
      localOpenBrowsers,
      pidsInfo,
      count: localOpenBrowsers.length
    }
  } catch (error) {
    console.error('获取本地打开窗口详情失败:', error.message)
    return {
      success: false,
      localOpenBrowsers: [],
      pidsInfo: {},
      error: error.message
    }
  }
}

module.exports = {
  openBrowser,
  closeBrowser,
  createBrowser,
  deleteBrowser,
  getBrowserDetail,
  addGroup,
  editGroup,
  deleteGroup,
  getGroupDetail,
  getGroupList,
  getBrowserList,
  getAllBrowserList,
  getLocalOpenBrowsersDetail,
  getBrowserWebSocketEndpoint,
  getPids,
  updatepartial,
  updateBrowserMemark,
  deleteBatchBrowser,
  getBrowserConciseList,
  getAlivePids,
  getAliveBrowsersPids,
  batchUpdateBrowserGroup,
  closeBrowsersBySeqs,
  batchUpdateProxy,
  request
}
