/**
 * @description 获取所有比特浏览器窗口列表的示例
 * @description 这个示例展示了如何使用 getAllBrowserList 方法来获取所有窗口，不受100个限制
 */

const { getAllBrowserList } = require('./request')

// 示例1: 获取所有窗口
async function example1() {
  console.log('=== 示例1: 获取所有窗口 ===')
  try {
    const allBrowsers = await getAllBrowserList()
    console.log(`总共获取到 ${allBrowsers.length} 个浏览器窗口`)
    
    // 显示前5个窗口的信息
    console.log('\n前5个窗口信息:')
    allBrowsers.slice(0, 5).forEach((browser, index) => {
      console.log(`${index + 1}. ID: ${browser.id}, 名称: ${browser.name}, 状态: ${browser.status}`)
    })
  } catch (error) {
    console.error('获取所有窗口失败:', error.message)
  }
}

// 示例2: 根据分组获取窗口
async function example2() {
  console.log('\n=== 示例2: 根据分组获取窗口 ===')
  try {
    // 假设你有一个分组ID，替换为实际的分组ID
    const groupId = 'your-group-id'
    const browsers = await getAllBrowserList({ groupId })
    console.log(`分组 ${groupId} 中共有 ${browsers.length} 个浏览器窗口`)
  } catch (error) {
    console.error('根据分组获取窗口失败:', error.message)
  }
}

// 示例3: 根据名称模糊查询窗口
async function example3() {
  console.log('\n=== 示例3: 根据名称模糊查询窗口 ===')
  try {
    const searchName = 'test' // 替换为你要搜索的名称
    const browsers = await getAllBrowserList({ name: searchName })
    console.log(`名称包含 "${searchName}" 的窗口共有 ${browsers.length} 个`)
    
    browsers.forEach((browser, index) => {
      console.log(`${index + 1}. ID: ${browser.id}, 名称: ${browser.name}`)
    })
  } catch (error) {
    console.error('根据名称查询窗口失败:', error.message)
  }
}

// 示例4: 获取活跃窗口统计
async function example4() {
  console.log('\n=== 示例4: 获取活跃窗口统计 ===')
  try {
    const allBrowsers = await getAllBrowserList()
    
    const stats = {
      total: allBrowsers.length,
      active: 0,
      inactive: 0,
      byStatus: {}
    }
    
    allBrowsers.forEach(browser => {
      if (browser.status === 1) {
        stats.active++
      } else {
        stats.inactive++
      }
      
      stats.byStatus[browser.status] = (stats.byStatus[browser.status] || 0) + 1
    })
    
    console.log('窗口统计:')
    console.log(`总数: ${stats.total}`)
    console.log(`活跃: ${stats.active}`)
    console.log(`非活跃: ${stats.inactive}`)
    console.log('按状态分布:', stats.byStatus)
  } catch (error) {
    console.error('获取窗口统计失败:', error.message)
  }
}

// 主函数
async function main() {
  await example1()
  // await example2() // 取消注释并设置正确的分组ID来测试
  // await example3() // 取消注释并设置搜索名称来测试
  await example4()
}

// 运行示例
if (require.main === module) {
  main().catch(console.error)
}

module.exports = {
  example1,
  example2,
  example3,
  example4
}
